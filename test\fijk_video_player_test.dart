import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_chi_gua/widgets/fijk_video_player_widget.dart';
import 'package:flutter_chi_gua/models/content_block.dart';

void main() {
  group('FijkVideoPlayerWidget Tests', () {
    testWidgets('should create widget with VideoBlock', (WidgetTester tester) async {
      // 创建测试用的VideoBlock
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        platform: 'mp4',
        controls: true,
        autoplay: false,
      );

      // 构建Widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FijkVideoPlayerWidget(
              videoBlock: videoBlock,
            ),
          ),
        ),
      );

      // 验证Widget是否正确创建
      expect(find.byType(FijkVideoPlayerWidget), findsOneWidget);
    });

    testWidgets('should show loading indicator initially', (WidgetTester tester) async {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        platform: 'mp4',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FijkVideoPlayerWidget(
              videoBlock: videoBlock,
            ),
          ),
        ),
      );

      // 应该显示加载指示器
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('正在加载视频...'), findsOneWidget);
    });

    testWidgets('should show platform badge when platform is provided', (WidgetTester tester) async {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        platform: 'youtube',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FijkVideoPlayerWidget(
              videoBlock: videoBlock,
            ),
          ),
        ),
      );

      // 等待Widget构建完成
      await tester.pump();

      // 应该有平台标识（虽然可能在加载状态下不可见）
      expect(find.byType(FijkVideoPlayerWidget), findsOneWidget);
    });

    test('should handle different video formats', () {
      // 测试不同的视频格式
      const formats = [
        'https://example.com/video.mp4',
        'https://example.com/stream.m3u8',
        'https://www.youtube.com/watch?v=test',
        'https://www.bilibili.com/video/test',
      ];

      for (final format in formats) {
        final videoBlock = VideoBlock(
          src: format,
          platform: _detectPlatform(format),
        );

        expect(videoBlock.src, equals(format));
        expect(videoBlock.platform, isNotNull);
      }
    });

    test('should handle VideoBlock properties correctly', () {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        poster: 'https://example.com/poster.jpg',
        title: 'Test Video',
        width: 1920,
        height: 1080,
        controls: true,
        autoplay: false,
        loop: true,
        muted: false,
        platform: 'mp4',
      );

      expect(videoBlock.src, equals('https://example.com/test.mp4'));
      expect(videoBlock.poster, equals('https://example.com/poster.jpg'));
      expect(videoBlock.title, equals('Test Video'));
      expect(videoBlock.width, equals(1920));
      expect(videoBlock.height, equals(1080));
      expect(videoBlock.controls, isTrue);
      expect(videoBlock.autoplay, isFalse);
      expect(videoBlock.loop, isTrue);
      expect(videoBlock.muted, isFalse);
      expect(videoBlock.platform, equals('mp4'));
    });
  });
}

/// 辅助函数：检测视频平台
String _detectPlatform(String url) {
  if (url.contains('youtube.com') || url.contains('youtu.be')) {
    return 'youtube';
  } else if (url.contains('bilibili.com')) {
    return 'bilibili';
  } else if (url.contains('vimeo.com')) {
    return 'vimeo';
  } else if (url.contains('.m3u8')) {
    return 'hls';
  } else if (url.contains('.mp4')) {
    return 'mp4';
  } else {
    return 'unknown';
  }
}
