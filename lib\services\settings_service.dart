import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 设置数据管理服务
/// 负责管理应用的配置信息，包括多个Base URL等设置
class SettingsService extends ChangeNotifier {
  static const String _baseUrlKey = 'base_url';
  static const String _secondBaseUrlKey = 'second_base_url';
  static const String _defaultBaseUrl = 'https://agree.blinkit.top';
  static const String _defaultSecondBaseUrl = 'http://8556ck.cc';

  static SettingsService? _instance;
  SharedPreferences? _prefs;

  String _baseUrl = _defaultBaseUrl;
  String _secondBaseUrl = _defaultSecondBaseUrl;
  bool _isInitialized = false;
  
  /// 单例模式获取实例
  static SettingsService get instance {
    _instance ??= SettingsService._internal();
    return _instance!;
  }
  
  SettingsService._internal();
  
  /// 当前的Base URL
  String get baseUrl => _baseUrl;

  /// 当前的第二个Base URL
  String get secondBaseUrl => _secondBaseUrl;

  /// 默认的Base URL
  String get defaultBaseUrl => _defaultBaseUrl;

  /// 默认的第二个Base URL
  String get defaultSecondBaseUrl => _defaultSecondBaseUrl;

  /// 获取所有Base URL列表
  List<String> get allBaseUrls => [_baseUrl, _secondBaseUrl];

  /// 是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 初始化设置服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _baseUrl = _prefs?.getString(_baseUrlKey) ?? _defaultBaseUrl;
      _secondBaseUrl = _prefs?.getString(_secondBaseUrlKey) ?? _defaultSecondBaseUrl;
      _isInitialized = true;

      print('SettingsService: Initialized with baseUrl: $_baseUrl, secondBaseUrl: $_secondBaseUrl');
    } catch (e) {
      print('SettingsService: Failed to initialize: $e');
      _baseUrl = _defaultBaseUrl;
      _secondBaseUrl = _defaultSecondBaseUrl;
      _isInitialized = true;
    }
  }
  
  /// 设置Base URL
  Future<bool> setBaseUrl(String url) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      // 验证URL格式
      if (!_isValidUrl(url)) {
        throw Exception('无效的URL格式');
      }
      
      // 清理URL（移除末尾的斜杠）
      final cleanUrl = _cleanUrl(url);
      
      // 保存到SharedPreferences
      final success = await _prefs?.setString(_baseUrlKey, cleanUrl) ?? false;
      
      if (success) {
        _baseUrl = cleanUrl;
        notifyListeners();
        print('SettingsService: Base URL updated to: $cleanUrl');
        return true;
      } else {
        throw Exception('保存设置失败');
      }
    } catch (e) {
      print('SettingsService: Failed to set base URL: $e');
      return false;
    }
  }
  
  /// 设置第二个Base URL
  Future<bool> setSecondBaseUrl(String url) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // 验证URL格式
      if (!_isValidUrl(url)) {
        throw Exception('无效的URL格式');
      }

      // 清理URL（移除末尾的斜杠）
      final cleanUrl = _cleanUrl(url);

      // 保存到SharedPreferences
      final success = await _prefs?.setString(_secondBaseUrlKey, cleanUrl) ?? false;

      if (success) {
        _secondBaseUrl = cleanUrl;
        notifyListeners();
        print('SettingsService: Second Base URL updated to: $cleanUrl');
        return true;
      } else {
        throw Exception('保存设置失败');
      }
    } catch (e) {
      print('SettingsService: Failed to set second base URL: $e');
      return false;
    }
  }

  /// 重置为默认Base URL
  Future<bool> resetToDefault() async {
    final result1 = await setBaseUrl(_defaultBaseUrl);
    final result2 = await setSecondBaseUrl(_defaultSecondBaseUrl);
    return result1 && result2;
  }
  
  /// 验证URL格式
  bool _isValidUrl(String url) {
    if (url.isEmpty) return false;
    
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && 
             (uri.scheme == 'http' || uri.scheme == 'https') &&
             uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }
  
  /// 清理URL（移除末尾的斜杠）
  String _cleanUrl(String url) {
    return url.endsWith('/') ? url.substring(0, url.length - 1) : url;
  }
  
  /// 验证URL是否可访问
  Future<bool> validateUrl(String url) async {
    try {
      if (!_isValidUrl(url)) return false;
      
      // 这里可以添加网络请求来验证URL是否可访问
      // 为了简化，暂时只做格式验证
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 获取分类URL
  String getCategoryUrl([String? category]) {
    if (category != null && category.isNotEmpty) {
      return '$_baseUrl/category/$category/';
    }
    return '$_baseUrl/category/wpcz/';
  }
  
  /// 获取完整URL（使用第一个baseUrl）
  String getFullUrl(String path) {
    return getFullUrlForSource(path, 0);
  }

  /// 获取指定数据源的完整URL
  /// [path] 路径
  /// [sourceIndex] 数据源索引：0=第一个baseUrl, 1=第二个baseUrl
  String getFullUrlForSource(String path, int sourceIndex) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    final baseUrl = sourceIndex == 0 ? _baseUrl : _secondBaseUrl;

    if (path.startsWith('/')) {
      return '$baseUrl$path';
    }

    return '$baseUrl/$path';
  }

  /// 根据URL判断数据源索引
  int getSourceIndexFromUrl(String url) {
    if (url.startsWith(_baseUrl)) {
      return 0;
    } else if (url.startsWith(_secondBaseUrl)) {
      return 1;
    }
    return 0; // 默认返回第一个源
  }

  /// 获取数据源名称
  String getSourceName(int sourceIndex) {
    switch (sourceIndex) {
      case 0:
        return '数据源1';
      case 1:
        return '数据源2';
      default:
        return '未知源';
    }
  }
  
  /// 清除所有设置
  Future<bool> clearAllSettings() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final success = await _prefs?.clear() ?? false;
      if (success) {
        _baseUrl = _defaultBaseUrl;
        _secondBaseUrl = _defaultSecondBaseUrl;
        notifyListeners();
        print('SettingsService: All settings cleared');
        return true;
      }
      return false;
    } catch (e) {
      print('SettingsService: Failed to clear settings: $e');
      return false;
    }
  }
  
  /// 导出设置为Map
  Map<String, dynamic> exportSettings() {
    return {
      'baseUrl': _baseUrl,
      'secondBaseUrl': _secondBaseUrl,
      'defaultBaseUrl': _defaultBaseUrl,
      'defaultSecondBaseUrl': _defaultSecondBaseUrl,
      'isInitialized': _isInitialized,
    };
  }

  /// 从Map导入设置
  Future<bool> importSettings(Map<String, dynamic> settings) async {
    try {
      final baseUrl = settings['baseUrl'] as String?;
      final secondBaseUrl = settings['secondBaseUrl'] as String?;

      bool result1 = true;
      bool result2 = true;

      if (baseUrl != null) {
        result1 = await setBaseUrl(baseUrl);
      }

      if (secondBaseUrl != null) {
        result2 = await setSecondBaseUrl(secondBaseUrl);
      }

      return result1 && result2;
    } catch (e) {
      print('SettingsService: Failed to import settings: $e');
      return false;
    }
  }
}
