package com.flutter.chi.gua.flutter_chi_gua

import android.content.Intent
import android.net.Uri
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.flutter.chi.gua/video_launcher"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "launchVideoPlayer" -> {
                    val videoUrl = call.argument<String>("url")
                    if (videoUrl != null) {
                        launchVideoPlayer(videoUrl, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Video URL is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun launchVideoPlayer(videoUrl: String, result: MethodChannel.Result) {
        try {
            val uri = Uri.parse(videoUrl)
            val urlLower = videoUrl.lowercase()

            // 尝试多种方式启动播放器
            val intents = mutableListOf<Intent>()

            // 方式1: 针对 m3u8 格式使用特定的 MIME 类型
            if (urlLower.contains(".m3u8") || urlLower.contains("m3u8")) {
                // HLS 流媒体的多种 MIME 类型
                val hlsMimeTypes = arrayOf(
                    "application/vnd.apple.mpegurl",
                    "application/x-mpegURL",
                    "application/mpegurl",
                    "video/mp2t",
                    "video/x-mpegts"
                )

                for (mimeType in hlsMimeTypes) {
                    intents.add(Intent(Intent.ACTION_VIEW).apply {
                        setDataAndType(uri, mimeType)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    })
                }
            }

            // 方式2: 通用视频 MIME 类型
            intents.add(Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, "video/*")
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            })

            // 方式3: 不指定 MIME 类型，让系统自动判断
            intents.add(Intent(Intent.ACTION_VIEW).apply {
                data = uri
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            })

            // 尝试每种方式，直到找到可用的
            var launched = false
            for (intent in intents) {
                if (intent.resolveActivity(packageManager) != null) {
                    try {
                        startActivity(Intent.createChooser(intent, "选择播放器"))
                        launched = true
                        break
                    } catch (e: Exception) {
                        // 继续尝试下一种方式
                        continue
                    }
                }
            }

            if (launched) {
                result.success(true)
            } else {
                result.error("NO_APP", "没有找到可以播放此视频的应用", null)
            }
        } catch (e: Exception) {
            result.error("LAUNCH_ERROR", "启动播放器失败: ${e.message}", null)
        }
    }
}
