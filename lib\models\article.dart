import 'content_block.dart';

class Article {
  final String id;
  final String title;
  final String url;
  final String excerpt;
  final String author;
  final DateTime publishDate;
  final List<String> categories;
  final String? imageUrl;
  final String? content;
  final List<ContentBlock>? contentBlocks;
  final int sourceIndex; // 数据源索引：0=第一个源，1=第二个源
  final String sourceName; // 数据源名称

  Article({
    required this.id,
    required this.title,
    required this.url,
    required this.excerpt,
    required this.author,
    required this.publishDate,
    required this.categories,
    this.imageUrl,
    this.content,
    this.contentBlocks,
    this.sourceIndex = 0,
    this.sourceName = '数据源1',
  });

  factory Article.fromMap(Map<String, dynamic> map) {
    List<ContentBlock>? contentBlocks;
    if (map['contentBlocks'] != null) {
      final blocksList = map['contentBlocks'] as List<dynamic>;
      contentBlocks = blocksList
          .map((block) => ContentBlock.fromMap(Map<String, dynamic>.from(block)))
          .toList();
    }

    return Article(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      url: map['url'] ?? '',
      excerpt: map['excerpt'] ?? '',
      author: map['author'] ?? '',
      publishDate: DateTime.tryParse(map['publishDate'] ?? '') ?? DateTime.now(),
      categories: List<String>.from(map['categories'] ?? []),
      imageUrl: map['imageUrl'],
      content: map['content'],
      contentBlocks: contentBlocks,
      sourceIndex: map['sourceIndex'] ?? 0,
      sourceName: map['sourceName'] ?? '数据源1',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'excerpt': excerpt,
      'author': author,
      'publishDate': publishDate.toIso8601String(),
      'categories': categories,
      'imageUrl': imageUrl,
      'content': content,
      'contentBlocks': contentBlocks?.map((block) => block.toMap()).toList(),
      'sourceIndex': sourceIndex,
      'sourceName': sourceName,
    };
  }

  Article copyWith({
    String? id,
    String? title,
    String? url,
    String? excerpt,
    String? author,
    DateTime? publishDate,
    List<String>? categories,
    String? imageUrl,
    String? content,
    List<ContentBlock>? contentBlocks,
    int? sourceIndex,
    String? sourceName,
  }) {
    return Article(
      id: id ?? this.id,
      title: title ?? this.title,
      url: url ?? this.url,
      excerpt: excerpt ?? this.excerpt,
      author: author ?? this.author,
      publishDate: publishDate ?? this.publishDate,
      categories: categories ?? this.categories,
      imageUrl: imageUrl ?? this.imageUrl,
      content: content ?? this.content,
      contentBlocks: contentBlocks ?? this.contentBlocks,
      sourceIndex: sourceIndex ?? this.sourceIndex,
      sourceName: sourceName ?? this.sourceName,
    );
  }

  @override
  String toString() {
    return 'Article(id: $id, title: $title, author: $author, publishDate: $publishDate, source: $sourceName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Article && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
