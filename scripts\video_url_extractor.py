#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频URL提取器
专门用于提取网页中的视频流URL
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, unquote
import sys

class VideoURLExtractor:
    def __init__(self):
        self.base_url = "https://agree.blinkit.top"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def extract_video_urls(self, url):
        """提取视频URL"""
        print(f"🎬 提取视频URL: {url}")
        
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            html_content = response.text
            
            # 提取所有可能的视频URL
            video_urls = self._find_all_video_urls(html_content)
            
            result = {
                'page_url': url,
                'video_urls': video_urls,
                'total_videos': len(video_urls)
            }
            
            self._print_results(result)
            return result
            
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            return None
    
    def _find_all_video_urls(self, html_content):
        """查找所有视频URL"""
        video_urls = []
        
        # 1. HLS流 (.m3u8)
        hls_pattern = r'https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*'
        hls_urls = re.findall(hls_pattern, html_content, re.IGNORECASE)
        for url in hls_urls:
            video_urls.append({
                'type': 'HLS',
                'url': self._clean_url(url),
                'format': 'm3u8'
            })
        
        # 2. MP4文件
        mp4_pattern = r'https?://[^\s"\'<>]+\.mp4[^\s"\'<>]*'
        mp4_urls = re.findall(mp4_pattern, html_content, re.IGNORECASE)
        for url in mp4_urls:
            video_urls.append({
                'type': 'MP4',
                'url': self._clean_url(url),
                'format': 'mp4'
            })
        
        # 3. WebM文件
        webm_pattern = r'https?://[^\s"\'<>]+\.webm[^\s"\'<>]*'
        webm_urls = re.findall(webm_pattern, html_content, re.IGNORECASE)
        for url in webm_urls:
            video_urls.append({
                'type': 'WebM',
                'url': self._clean_url(url),
                'format': 'webm'
            })
        
        # 4. 其他视频格式
        other_video_patterns = [
            (r'https?://[^\s"\'<>]+\.avi[^\s"\'<>]*', 'AVI', 'avi'),
            (r'https?://[^\s"\'<>]+\.mov[^\s"\'<>]*', 'MOV', 'mov'),
            (r'https?://[^\s"\'<>]+\.wmv[^\s"\'<>]*', 'WMV', 'wmv'),
            (r'https?://[^\s"\'<>]+\.flv[^\s"\'<>]*', 'FLV', 'flv'),
            (r'https?://[^\s"\'<>]+\.mkv[^\s"\'<>]*', 'MKV', 'mkv'),
            (r'https?://[^\s"\'<>]+\.3gp[^\s"\'<>]*', '3GP', '3gp'),
        ]
        
        for pattern, video_type, format_name in other_video_patterns:
            urls = re.findall(pattern, html_content, re.IGNORECASE)
            for url in urls:
                video_urls.append({
                    'type': video_type,
                    'url': self._clean_url(url),
                    'format': format_name
                })
        
        # 5. Blob URLs
        blob_pattern = r'blob:[^\s"\'<>]+'
        blob_urls = re.findall(blob_pattern, html_content, re.IGNORECASE)
        for url in blob_urls:
            video_urls.append({
                'type': 'Blob',
                'url': url,
                'format': 'blob'
            })
        
        # 6. 视频流域名模式
        streaming_domains = [
            r'https?://[^\s"\'<>]*(?:video|stream|media|hls|dash)[^\s"\'<>]*',
            r'https?://[^\s"\'<>]*\.(?:qzkj|video|stream)\.tech[^\s"\'<>]*',
            r'https?://[^\s"\'<>]*\.(?:cloudfront|amazonaws|aliyun)\.com[^\s"\'<>]*video[^\s"\'<>]*'
        ]
        
        for pattern in streaming_domains:
            urls = re.findall(pattern, html_content, re.IGNORECASE)
            for url in urls:
                clean_url = self._clean_url(url)
                if self._is_likely_video_url(clean_url):
                    video_urls.append({
                        'type': 'Stream',
                        'url': clean_url,
                        'format': 'stream'
                    })
        
        # 7. 从data-config属性中提取（特殊处理）
        data_config_pattern = r'data-config\s*=\s*["\']([^"\']+)["\']'
        data_config_matches = re.findall(data_config_pattern, html_content, re.IGNORECASE)
        for config_json in data_config_matches:
            try:
                # 解析JSON配置
                config_data = json.loads(config_json)
                if 'video' in config_data and 'url' in config_data['video']:
                    video_url = config_data['video']['url']
                    video_type = config_data['video'].get('type', 'unknown')
                    video_urls.append({
                        'type': f'DataConfig-{video_type.upper()}',
                        'url': self._clean_url(video_url),
                        'format': video_type,
                        'config': config_data
                    })
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试正则提取
                url_in_config = re.search(r'"url"\s*:\s*"([^"]+)"', config_json)
                if url_in_config:
                    video_urls.append({
                        'type': 'DataConfig-Raw',
                        'url': self._clean_url(url_in_config.group(1)),
                        'format': 'config_raw'
                    })

        # 8. 从JavaScript变量中提取
        js_video_patterns = [
            r'videoUrl\s*[:=]\s*["\']([^"\']+)["\']',
            r'video_url\s*[:=]\s*["\']([^"\']+)["\']',
            r'src\s*[:=]\s*["\']([^"\']*(?:mp4|m3u8|webm)[^"\']*)["\']',
            r'url\s*[:=]\s*["\']([^"\']*(?:video|stream)[^"\']*)["\']',
            r'"url"\s*:\s*"([^"]*(?:mp4|m3u8|webm|hls)[^"]*)"'
        ]

        for pattern in js_video_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                clean_url = self._clean_url(match)
                if self._is_likely_video_url(clean_url):
                    video_urls.append({
                        'type': 'JavaScript',
                        'url': clean_url,
                        'format': 'js_extracted'
                    })
        
        # 去重
        seen_urls = set()
        unique_videos = []
        for video in video_urls:
            if video['url'] not in seen_urls:
                seen_urls.add(video['url'])
                unique_videos.append(video)
        
        return unique_videos
    
    def _clean_url(self, url):
        """清理URL"""
        # 移除转义字符
        url = url.replace('\\/', '/')
        url = url.replace('\\"', '"')
        
        # URL解码
        try:
            url = unquote(url)
        except:
            pass
        
        # 移除末尾的标点符号
        url = url.rstrip('.,;:!?')
        
        return url
    
    def _is_likely_video_url(self, url):
        """判断是否可能是视频URL"""
        if not url or len(url) < 10:
            return False
        
        # 检查是否包含视频相关关键词
        video_keywords = [
            'video', 'stream', 'media', 'hls', 'dash', 'mp4', 'm3u8', 'webm',
            'avi', 'mov', 'wmv', 'flv', 'mkv', '3gp', 'blob:'
        ]
        
        url_lower = url.lower()
        return any(keyword in url_lower for keyword in video_keywords)
    
    def _print_results(self, result):
        """打印结果"""
        print(f"\n📊 视频URL提取结果:")
        print(f"  页面: {result['page_url']}")
        print(f"  找到视频: {result['total_videos']} 个")
        
        if result['video_urls']:
            print(f"\n🎥 视频列表:")
            for i, video in enumerate(result['video_urls'], 1):
                print(f"  {i:2d}. [{video['type']}] {video['format']}")
                print(f"      {video['url']}")
                print()
        else:
            print("  ❌ 未找到视频URL")
    
    def test_video_url(self, video_url):
        """测试视频URL是否可访问"""
        print(f"🧪 测试视频URL: {video_url}")
        
        try:
            response = requests.head(video_url, headers=self.headers, timeout=10)
            print(f"  状态码: {response.status_code}")
            print(f"  Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            print(f"  Content-Length: {response.headers.get('Content-Length', 'N/A')}")
            
            if response.status_code == 200:
                print("  ✅ URL可访问")
                return True
            else:
                print("  ❌ URL不可访问")
                return False
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            return False

def main():
    extractor = VideoURLExtractor()
    
    # 测试URL列表
    test_urls = [
        "https://agree.blinkit.top/archives/214691/",
        "https://agree.blinkit.top/archives/214491/",
        "https://agree.blinkit.top/archives/214603/"
    ]
    
    all_results = []
    
    for url in test_urls:
        print(f"\n{'='*80}")
        result = extractor.extract_video_urls(url)
        if result:
            all_results.append(result)
            
            # 测试找到的视频URL
            for video in result['video_urls'][:2]:  # 只测试前2个
                print(f"\n🧪 测试视频:")
                extractor.test_video_url(video['url'])
        
        print(f"{'='*80}")
    
    # 保存所有结果
    if all_results:
        with open('extracted_video_urls.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 所有结果已保存到: extracted_video_urls.json")
        
        # 打印汇总
        total_videos = sum(r['total_videos'] for r in all_results)
        print(f"\n📈 汇总统计:")
        print(f"  分析页面: {len(all_results)} 个")
        print(f"  找到视频: {total_videos} 个")
        
        if total_videos > 0:
            print(f"\n🎬 所有视频URL:")
            video_count = 0
            for result in all_results:
                for video in result['video_urls']:
                    video_count += 1
                    print(f"  {video_count:2d}. [{video['type']}] {video['url']}")

if __name__ == '__main__':
    main()
