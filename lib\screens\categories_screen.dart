import 'package:flutter/material.dart';
import '../models/category.dart';
import '../services/category_service.dart';
import 'category_articles_screen.dart';
import 'search_results_screen.dart';

/// 分类页面
/// 展示所有可用的分类列表
class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> {
  final CategoryService _categoryService = CategoryService();
  final TextEditingController _searchController = TextEditingController();
  
  List<Category> _allCategories = [];
  List<Category> _filteredCategories = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 加载分类数据
  Future<void> _loadCategories() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final categories = await _categoryService.getCategories();
      if (!mounted) return;

      setState(() {
        _allCategories = categories;
        _filteredCategories = categories;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 搜索变化监听
  void _onSearchChanged() {
    // 移除实时过滤反馈，只在搜索框为空时重置显示所有分类
    final query = _searchController.text;
    if (query.trim().isEmpty) {
      setState(() {
        _filteredCategories = _allCategories;
      });
    }
    // 不再进行实时本地分类过滤，避免显示"未找到匹配的分类"消息
  }

  /// 执行全站搜索
  void _performGlobalSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SearchResultsScreen(query: query),
        ),
      );
    }
  }

  /// 点击分类
  void _onCategoryTap(Category category) {
    // 调试信息：确认分类URL是否正确
    print('CategoriesScreen: Navigating to category: ${category.name}, URL: ${category.url}');

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoryArticlesScreen(category: category),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分类'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCategories,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索分类或全站内容...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_searchController.text.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _onSearchChanged();
                      },
                      tooltip: '清除',
                    ),
                  IconButton(
                    icon: const Icon(Icons.travel_explore),
                    onPressed: _performGlobalSearch,
                    tooltip: '全站搜索',
                  ),
                ],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surface,
            ),
            onSubmitted: (_) => _performGlobalSearch(),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  '输入关键词后点击🌐按钮或按回车进行全站搜索',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在加载分类...'),
            ],
          ),
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                '加载失败',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  _errorMessage,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadCategories,
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    if (_filteredCategories.isEmpty) {
      return Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.category,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                '暂无分类',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                '请稍后重试或检查网络连接',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    return _buildCategoryGrid();
  }

  /// 构建分类网格
  Widget _buildCategoryGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 2.0, // 增加高度，从2.5改为2.0
      ),
      itemCount: _filteredCategories.length,
      itemBuilder: (context, index) {
        final category = _filteredCategories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  /// 构建分类卡片
  Widget _buildCategoryCard(Category category) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _onCategoryTap(category),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(8), // 减少padding，从12改为8
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min, // 添加这个属性
            children: [
              Icon(
                _getCategoryIcon(category.name),
                size: 20, // 减少图标大小，从24改为20
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 6), // 减少间距，从8改为6
              Flexible( // 使用Flexible包装文本
                child: Text(
                  category.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 12, // 减少字体大小
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (!category.isAvailable) ...[
                const SizedBox(height: 2), // 减少间距，从4改为2
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1), // 减少padding
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(6), // 减少圆角，从8改为6
                  ),
                  child: const Text(
                    '维护中',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 9, // 减少字体大小，从10改为9
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 根据分类名称获取图标
  IconData _getCategoryIcon(String categoryName) {
    if (categoryName.contains('今日') || categoryName.contains('热门')) {
      return Icons.whatshot;
    } else if (categoryName.contains('榜单')) {
      return Icons.leaderboard;
    } else if (categoryName.contains('学生') || categoryName.contains('校园')) {
      return Icons.school;
    } else if (categoryName.contains('娱乐') || categoryName.contains('看片')) {
      return Icons.movie;
    } else if (categoryName.contains('必看') || categoryName.contains('大瓜')) {
      return Icons.star;
    } else if (categoryName.contains('网红') || categoryName.contains('明星')) {
      return Icons.person;
    } else if (categoryName.contains('海外')) {
      return Icons.public;
    } else if (categoryName.contains('新闻')) {
      return Icons.newspaper;
    } else if (categoryName.contains('剧场') || categoryName.contains('短剧')) {
      return Icons.theaters;
    } else if (categoryName.contains('原创')) {
      return Icons.create;
    } else if (categoryName.contains('知识')) {
      return Icons.lightbulb;
    } else if (categoryName.contains('领导') || categoryName.contains('干部')) {
      return Icons.business;
    } else {
      return Icons.category;
    }
  }
}
