import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../services/image_decryptor.dart';

class EncryptedNetworkImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const EncryptedNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<EncryptedNetworkImage> createState() => _EncryptedNetworkImageState();
}

class _EncryptedNetworkImageState extends State<EncryptedNetworkImage> {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAndDecryptImage();
  }

  @override
  void didUpdateWidget(EncryptedNetworkImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadAndDecryptImage();
    }
  }

  Future<void> _loadAndDecryptImage() async {
    if (!mounted) return;

    print('EncryptedNetworkImage: Loading image from URL: ${widget.imageUrl}');

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
      _imageBytes = null;
    });

    try {
      // 下载加密的图片数据
      final response = await http.get(
        Uri.parse(widget.imageUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
      );

      print('EncryptedNetworkImage: HTTP response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final encryptedBytes = response.bodyBytes;
        
        // 首先检查是否是普通图片（未加密）
        if (ImageDecryptor.isValidImageBytes(encryptedBytes)) {
          // 如果是有效的图片格式，直接使用
          if (!mounted) return;
          setState(() {
            _imageBytes = encryptedBytes;
            _isLoading = false;
          });
          return;
        }

        // 尝试解密图片
        final decryptedBytes = ImageDecryptor.decryptImageBytes(encryptedBytes);

        if (decryptedBytes != null && ImageDecryptor.isValidImageBytes(decryptedBytes)) {
          if (!mounted) return;
          setState(() {
            _imageBytes = decryptedBytes;
            _isLoading = false;
          });
        } else {
          // 解密失败，显示错误状态而不是尝试使用原始数据
          if (!mounted) return;
          setState(() {
            _hasError = true;
            _errorMessage = '图片解密失败';
            _isLoading = false;
          });
        }
      } else {
        throw Exception('Failed to load image: HTTP ${response.statusCode}');
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.placeholder ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey[200],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
    }

    if (_hasError || _imageBytes == null) {
      return widget.errorWidget ??
          Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_outlined,
                  color: Colors.grey[400],
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  '图片加载中...',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
    }

    return Image.memory(
      _imageBytes!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      errorBuilder: (context, error, stackTrace) {
        return widget.errorWidget ??
            Container(
              width: widget.width,
              height: widget.height,
              color: Colors.grey[300],
              child: const Center(
                child: Icon(
                  Icons.broken_image,
                  color: Colors.grey,
                  size: 48,
                ),
              ),
            );
      },
    );
  }
}

/// 简化的加密图片组件，用于快速替换Image.network
class EncryptedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;

  const EncryptedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
  });

  @override
  Widget build(BuildContext context) {
    return EncryptedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
    );
  }
}
