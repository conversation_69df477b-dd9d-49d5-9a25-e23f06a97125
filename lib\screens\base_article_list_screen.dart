import 'package:flutter/material.dart';
import '../models/article.dart';
import '../services/web_scraping_service.dart';
import 'article_detail_screen.dart';

/// 文章列表页面基类
/// 提供通用的文章列表功能，子类实现具体的差异化逻辑
abstract class BaseArticleListScreen extends StatefulWidget {
  const BaseArticleListScreen({super.key});
}

abstract class BaseArticleListScreenState<T extends BaseArticleListScreen> extends State<T> {
  final WebScrapingService webScrapingService = WebScrapingService();
  final ScrollController scrollController = ScrollController();

  List<Article> articles = [];
  bool isLoading = false;
  bool hasError = false;
  String errorMessage = '';
  int currentPage = 1;
  bool hasMoreData = true;

  // 抽象方法，由子类实现
  String getAppBarTitle();
  String? getCategoryUrl();
  Future<void> onRefresh();
  Widget buildArticleCard(Article article);
  String formatDate(DateTime date);
  Widget buildEmptyState();
  Widget buildErrorState();

  @override
  void initState() {
    super.initState();
    loadArticles();
    scrollController.addListener(onScroll);
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  /// 滚动监听
  void onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      if (!isLoading && hasMoreData) {
        loadMoreArticles();
      }
    }
  }

  /// 加载文章
  Future<void> loadArticles() async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
      hasError = false;
      errorMessage = '';
    });

    try {
      final articleList = await webScrapingService.fetchArticles(
        page: 1,
        categoryUrl: getCategoryUrl(),
      );

      if (mounted) {
        setState(() {
          articles = articleList;
          currentPage = 1;
          hasMoreData = articleList.isNotEmpty;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          hasError = true;
          errorMessage = e.toString();
          isLoading = false;
        });
      }
    }
  }

  /// 加载更多文章
  Future<void> loadMoreArticles() async {
    if (isLoading || !hasMoreData) return;

    setState(() {
      isLoading = true;
    });

    try {
      final nextPage = currentPage + 1;
      final newArticles = await webScrapingService.fetchArticles(
        page: nextPage,
        categoryUrl: getCategoryUrl(),
      );

      if (mounted) {
        setState(() {
          if (newArticles.isNotEmpty) {
            articles.addAll(newArticles);
            currentPage = nextPage;
          } else {
            hasMoreData = false;
          }
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载更多失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 跳转到文章详情
  void navigateToArticleDetail(Article article) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArticleDetailScreen(article: article),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(getAppBarTitle()),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: onRefresh,
          ),
        ],
      ),
      body: buildBody(),
    );
  }

  Widget buildBody() {
    if (hasError && articles.isEmpty) {
      return buildErrorState();
    }

    if (articles.isEmpty && isLoading) {
      return const Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在加载文章...'),
            ],
          ),
        ),
      );
    }

    if (articles.isEmpty) {
      return buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: onRefresh,
      child: ListView.builder(
        controller: scrollController,
        itemCount: articles.length + (hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == articles.length) {
            return buildLoadingIndicator();
          }

          return InkWell(
            onTap: () => navigateToArticleDetail(articles[index]),
            child: buildArticleCard(articles[index]),
          );
        },
      ),
    );
  }

  Widget buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: isLoading
          ? const CircularProgressIndicator()
          : const Text('已加载全部内容'),
    );
  }
}
