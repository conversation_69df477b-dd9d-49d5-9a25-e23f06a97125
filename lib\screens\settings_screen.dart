import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/settings_service.dart';

/// 设置页面
/// 提供Base URL配置和其他应用设置
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SettingsService _settingsService = SettingsService.instance;
  final TextEditingController _baseUrlController = TextEditingController();
  final TextEditingController _secondBaseUrlController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _hasChanges = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  @override
  void dispose() {
    _baseUrlController.dispose();
    _secondBaseUrlController.dispose();
    super.dispose();
  }

  /// 初始化设置
  Future<void> _initializeSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _settingsService.initialize();
      _baseUrlController.text = _settingsService.baseUrl;
      _secondBaseUrlController.text = _settingsService.secondBaseUrl;

      // 监听文本变化
      _baseUrlController.addListener(_onTextChanged);
      _secondBaseUrlController.addListener(_onTextChanged);
    } catch (e) {
      _showError('初始化设置失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 文本变化监听
  void _onTextChanged() {
    final hasChanges = _baseUrlController.text.trim() != _settingsService.baseUrl ||
                      _secondBaseUrlController.text.trim() != _settingsService.secondBaseUrl;
    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
        _errorMessage = null;
      });
    }
  }

  /// 验证URL格式
  String? _validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入Base URL';
    }

    final url = value.trim();
    
    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || (uri.scheme != 'http' && uri.scheme != 'https')) {
        return '请输入有效的HTTP或HTTPS URL';
      }
      if (!uri.hasAuthority) {
        return 'URL格式不正确';
      }
    } catch (e) {
      return 'URL格式不正确';
    }

    return null;
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final url1 = _baseUrlController.text.trim();
      final url2 = _secondBaseUrlController.text.trim();

      final success1 = await _settingsService.setBaseUrl(url1);
      final success2 = await _settingsService.setSecondBaseUrl(url2);

      if (success1 && success2) {
        setState(() {
          _hasChanges = false;
        });
        _showSuccess('设置保存成功');
      } else {
        _showError('保存设置失败');
      }
    } catch (e) {
      _showError('保存设置失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 重置为默认设置
  Future<void> _resetToDefault() async {
    final confirmed = await _showConfirmDialog(
      '重置设置',
      '确定要重置为默认设置吗？这将清除所有自定义配置。',
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final success = await _settingsService.resetToDefault();
      
      if (success) {
        _baseUrlController.text = _settingsService.baseUrl;
        _secondBaseUrlController.text = _settingsService.secondBaseUrl;
        setState(() {
          _hasChanges = false;
        });
        _showSuccess('已重置为默认设置');
      } else {
        _showError('重置设置失败');
      }
    } catch (e) {
      _showError('重置设置失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 复制URL到剪贴板
  Future<void> _copyUrl(String url) async {
    try {
      await Clipboard.setData(ClipboardData(text: url));
      _showSuccess('URL已复制到剪贴板');
    } catch (e) {
      _showError('复制失败: $e');
    }
  }

  /// 显示成功消息
  void _showSuccess(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示错误消息
  void _showError(String message) {
    setState(() {
      _errorMessage = message;
    });
    
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示确认对话框
  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_hasChanges)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _isLoading ? null : _saveSettings,
              tooltip: '保存设置',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildSettingsContent(),
    );
  }

  Widget _buildSettingsContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBaseUrlSection(),
            const SizedBox(height: 16),
            _buildSecondBaseUrlSection(),
            const SizedBox(height: 24),
            _buildActionButtons(),
            const SizedBox(height: 24),
            _buildInfoSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildBaseUrlSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.link, size: 20),
                const SizedBox(width: 8),
                Text(
                  '数据源1 URL 配置',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _baseUrlController,
              validator: _validateUrl,
              decoration: InputDecoration(
                labelText: '数据源1 URL',
                hintText: '例如: https://example.com',
                prefixIcon: const Icon(Icons.language),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () => _copyUrl(_settingsService.baseUrl),
                  tooltip: '复制当前URL',
                ),
                border: const OutlineInputBorder(),
                errorText: _errorMessage,
              ),
              keyboardType: TextInputType.url,
              textInputAction: TextInputAction.done,
              onFieldSubmitted: (_) => _saveSettings(),
            ),
            const SizedBox(height: 12),
            Text(
              '当前使用: ${_settingsService.baseUrl}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecondBaseUrlSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.link, size: 20),
                const SizedBox(width: 8),
                Text(
                  '数据源2 URL 配置',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _secondBaseUrlController,
              validator: _validateUrl,
              decoration: InputDecoration(
                labelText: '数据源2 URL',
                hintText: '例如: http://example.com',
                prefixIcon: const Icon(Icons.language),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () => _copyUrl(_settingsService.secondBaseUrl),
                  tooltip: '复制当前URL',
                ),
                border: const OutlineInputBorder(),
              ),
              keyboardType: TextInputType.url,
              textInputAction: TextInputAction.done,
              onFieldSubmitted: (_) => _saveSettings(),
            ),
            const SizedBox(height: 12),
            Text(
              '当前使用: ${_settingsService.secondBaseUrl}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isLoading || !_hasChanges ? null : _saveSettings,
            icon: const Icon(Icons.save),
            label: const Text('保存设置'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _isLoading ? null : _resetToDefault,
            icon: const Icon(Icons.restore),
            label: const Text('恢复默认'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, size: 20),
                const SizedBox(width: 8),
                Text(
                  '说明',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '• 应用支持两个数据源，搜索时会同时从两个源获取内容\n'
              '• 数据源1: ${_settingsService.defaultBaseUrl}\n'
              '• 数据源2: ${_settingsService.defaultSecondBaseUrl}\n'
              '• 修改后将影响搜索和内容获取\n'
              '• 请确保输入的URL可以正常访问\n'
              '• 当其中一个源不可用时，另一个源仍能正常工作',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
