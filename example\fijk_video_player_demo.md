# Fijkplayer 视频播放功能集成

## 功能概述

本次更新将原有的外部视频播放器替换为基于 `fijkplayer 0.11.0` 的内置视频播放器，提供更好的用户体验。

## 主要改进

### 1. 从外部播放器到内置播放器

**之前的实现**：
- 使用 `VideoPlayerWidget` 显示视频预览
- 点击后使用 `url_launcher` 在外部系统播放器中打开
- 只能生成缩略图，无法直接播放

**现在的实现**：
- 使用 `FijkVideoPlayerWidget` 提供完整的视频播放功能
- 支持应用内直接播放视频
- 提供播放控制界面

### 2. 支持的视频格式

- **MP4**: 标准视频格式
- **M3U8**: HLS 流媒体格式
- **其他格式**: 根据 fijkplayer 支持的格式

### 3. 播放功能特性

#### 基本播放控制
- ✅ 播放/暂停
- ✅ 进度控制
- ✅ 音量控制
- ✅ 全屏播放

#### 高级功能
- ✅ 自动重连（网络中断恢复）
- ✅ 快速定位播放
- ✅ 硬件解码支持
- ✅ 错误处理和重试

#### 用户界面
- ✅ 加载状态指示
- ✅ 错误状态显示
- ✅ 平台标识徽章
- ✅ 自定义控制面板

## 技术实现

### 依赖更新

```yaml
dependencies:
  # 新增视频播放器
  fijkplayer: ^0.11.0
  
  # 保留用于缩略图生成
  video_thumbnail: ^0.5.6
```

### 核心组件

#### FijkVideoPlayerWidget

```dart
class FijkVideoPlayerWidget extends StatefulWidget {
  final VideoBlock videoBlock;
  final VoidCallback? onTap;

  const FijkVideoPlayerWidget({
    super.key,
    required this.videoBlock,
    this.onTap,
  });
}
```

#### 播放器配置

```dart
// 设置播放器选项
_player.setOption(FijkOption.playerCategory, "enable-accurate-seek", 1);
_player.setOption(FijkOption.playerCategory, "mediacodec", 1);
_player.setOption(FijkOption.formatCategory, "reconnect", 5);
_player.setOption(FijkOption.formatCategory, "timeout", 20000000);
_player.setOption(FijkOption.formatCategory, "fflags", "fastseek");
```

### 与现有系统的兼容性

#### VideoBlock 模型兼容
- ✅ 完全兼容现有的 `VideoBlock` 数据结构
- ✅ 支持所有现有属性（src, poster, platform, etc.）
- ✅ 无需修改内容解析逻辑

#### ContentBlockRenderer 集成
```dart
/// 构建视频
Widget _buildVideo(BuildContext context, VideoBlock block) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8),
    child: FijkVideoPlayerWidget(
      videoBlock: block,
    ),
  );
}
```

## 错误处理

### 网络错误
- 自动重连机制
- 超时处理
- 用户友好的错误提示

### 格式不支持
- 优雅降级到错误状态
- 提供重试选项
- 显示具体错误信息

### 播放失败
- 状态监听和错误捕获
- 自动重试机制
- 手动重试按钮

## 性能优化

### 内存管理
- 播放器资源自动释放
- 组件销毁时清理资源
- 避免内存泄漏

### 播放优化
- 硬件解码支持
- 快速定位播放
- 预加载优化

## 测试覆盖

```bash
flutter test test/fijk_video_player_test.dart
```

测试内容：
- ✅ Widget 创建和渲染
- ✅ 加载状态显示
- ✅ 平台标识显示
- ✅ 不同视频格式处理
- ✅ VideoBlock 属性处理

## 使用示例

### 在文章详情页中
当文章内容包含视频时，会自动使用新的播放器：

```dart
// VideoBlock 会被自动渲染为 FijkVideoPlayerWidget
final videoBlock = VideoBlock(
  src: 'https://example.com/video.mp4',
  platform: 'mp4',
  controls: true,
  autoplay: false,
);
```

### 支持的平台标识
- YouTube (红色)
- Bilibili (蓝色)
- Vimeo (青色)
- HLS (绿色)
- MP4 (紫色)

## 迁移说明

### 已移除的组件
- ❌ `VideoPlayerWidget` (旧的视频播放器)
- ❌ 外部播放器依赖

### 保留的功能
- ✅ `video_thumbnail` (用于缩略图生成)
- ✅ `VideoBlock` 数据模型
- ✅ 内容解析逻辑

## 未来扩展

### 可能的增强功能
- 播放列表支持
- 画中画模式
- 字幕支持
- 播放速度调节
- 播放历史记录

### 性能监控
- 播放成功率统计
- 加载时间监控
- 错误类型分析
