import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/encrypted_network_image.dart';

/// 垂直滑动图片画廊页面
/// 支持上下滑动切换图片，双击缩放，捏合缩放等手势操作
class VerticalImageGalleryScreen extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;

  const VerticalImageGalleryScreen({
    super.key,
    required this.imageUrls,
    this.initialIndex = 0,
  });

  @override
  State<VerticalImageGalleryScreen> createState() => _VerticalImageGalleryScreenState();
}

class _VerticalImageGalleryScreenState extends State<VerticalImageGalleryScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isVisible = true;
  final Map<int, TransformationController> _transformationControllers = {};

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // 设置全屏模式
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  @override
  void dispose() {
    _pageController.dispose();
    // 清理所有TransformationController
    for (final controller in _transformationControllers.values) {
      controller.dispose();
    }
    _transformationControllers.clear();
    // 恢复系统UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  void _toggleUI() {
    setState(() {
      _isVisible = !_isVisible;
    });
  }

  void _handleDoubleTap(int index) {
    final controller = _transformationControllers[index];
    if (controller != null) {
      final currentScale = controller.value.getMaxScaleOnAxis();

      if (currentScale > 1.0) {
        // 如果已经缩放，则重置到原始大小
        controller.value = Matrix4.identity();
      } else {
        // 如果是原始大小，则放大到2倍
        controller.value = Matrix4.identity()..scale(2.0);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 主要的图片显示区域
          PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.vertical, // 垂直滑动
            itemCount: widget.imageUrls.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              return _buildImagePage(widget.imageUrls[index]);
            },
          ),

          // 顶部指示器和关闭按钮
          if (_isVisible)
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 图片计数器
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.imageUrls.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  // 关闭按钮
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // 底部指示器
          if (_isVisible && widget.imageUrls.length > 1)
            Positioned(
              bottom: MediaQuery.of(context).padding.bottom + 32,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.swipe_vertical,
                        color: Colors.white70,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '上下滑动切换图片',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePage(String imageUrl) {
    final index = widget.imageUrls.indexOf(imageUrl);

    // 为每个图片创建独立的TransformationController
    if (!_transformationControllers.containsKey(index)) {
      _transformationControllers[index] = TransformationController();
    }

    return GestureDetector(
      onTap: _toggleUI,
      onDoubleTap: () => _handleDoubleTap(index),
      child: InteractiveViewer(
        transformationController: _transformationControllers[index],
        minScale: 0.5,
        maxScale: 5.0,
        // 启用双击缩放
        onInteractionStart: (details) {
          // 当用户开始交互时，可以在这里添加逻辑
        },
        onInteractionUpdate: (details) {
          // 当用户正在交互时，可以在这里添加逻辑
        },
        onInteractionEnd: (details) {
          // 当用户结束交互时，可以在这里添加逻辑
        },
        child: SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Center(
            child: EncryptedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.contain,
              placeholder: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.white,
                    ),
                    SizedBox(height: 16),
                    Text(
                      '正在加载图片...',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              errorWidget: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image,
                      color: Colors.white54,
                      size: 64,
                    ),
                    SizedBox(height: 16),
                    Text(
                      '图片加载失败',
                      style: TextStyle(
                        color: Colors.white54,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '点击重试',
                      style: TextStyle(
                        color: Colors.white38,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
