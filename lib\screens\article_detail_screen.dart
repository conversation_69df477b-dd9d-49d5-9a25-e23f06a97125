import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/article.dart';
import '../models/content_block.dart';
import '../services/web_scraping_service.dart';
import '../widgets/content_block_renderer.dart';

class ArticleDetailScreen extends StatefulWidget {
  final Article article;

  const ArticleDetailScreen({
    super.key,
    required this.article,
  });

  @override
  State<ArticleDetailScreen> createState() => _ArticleDetailScreenState();
}

class _ArticleDetailScreenState extends State<ArticleDetailScreen> {
  final WebScrapingService _webScrapingService = WebScrapingService();
  Article? _articleWithContent;
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadArticleContent();
  }

  Future<void> _loadArticleContent() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final articleWithContent = await _webScrapingService.fetchArticleContent(widget.article);
      if (!mounted) return;

      setState(() {
        _articleWithContent = articleWithContent;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.article.title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontSize: 16), // 减小AppBar标题字体大小
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadArticleContent,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareArticle(),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在加载文章内容...'),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadArticleContent,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width - 32, // 减去左右padding
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildArticleHeader(),
            const SizedBox(height: 16),
            _buildArticleContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.article.title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: 18, // 减小正文标题字体大小
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            Icon(
              Icons.person,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              widget.article.author,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(width: 16),
            Icon(
              Icons.access_time,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              _formatDate(widget.article.publishDate),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            // 将标签移到同一行
            if (widget.article.categories.isNotEmpty) ...[
              const SizedBox(width: 16),
              ...widget.article.categories.map((category) {
                return Container(
                  margin: const EdgeInsets.only(right: 6),
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    category,
                    style: const TextStyle(
                      fontSize: 10, // 减小标签字体大小
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }),
            ],
          ],
        ),
        const Divider(height: 32),
      ],
    );
  }

  Widget _buildArticleContent() {
    final contentBlocks = _articleWithContent?.contentBlocks;

    if (contentBlocks == null || contentBlocks.isEmpty) {
      // 如果没有解析的内容块，尝试显示原始内容
      final content = _articleWithContent?.content;
      if (content != null && content.isNotEmpty) {
        return const Center(
          child: Text('内容解析中...'),
        );
      }
      return const Center(
        child: Text('暂无内容'),
      );
    }

    // 收集所有图片URL（递归处理嵌套内容块）
    final allImageUrls = <String>[];
    _collectImageUrls(contentBlocks, allImageUrls);

    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: contentBlocks.map((block) {
            // 为所有块传递图片列表，让嵌套的图片也能访问到完整列表
            if (block is ImageBlock) {
              final imageIndex = allImageUrls.indexOf(block.src);
              return ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: constraints.maxWidth,
                ),
                child: ContentBlockRenderer(
                  block: block,
                  allImageUrls: allImageUrls,
                  imageIndex: imageIndex,
                ),
              );
            }

            // 为非图片块也传递图片列表，以支持嵌套图片
            return ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: constraints.maxWidth,
              ),
              child: ContentBlockRenderer(
                block: block,
                allImageUrls: allImageUrls,
              ),
            );
          }).toList(),
        );
      },
    );
  }

  /// 递归收集所有图片URL
  void _collectImageUrls(List<ContentBlock> blocks, List<String> imageUrls) {
    for (final block in blocks) {
      if (block is ImageBlock) {
        imageUrls.add(block.src);
      } else if (block is MixedBlock) {
        _collectImageUrls(block.children, imageUrls);
      } else if (block is BlockquoteBlock) {
        _collectImageUrls(block.children, imageUrls);
      } else if (block is ListItemBlock) {
        _collectImageUrls(block.children, imageUrls);
      }
    }
  }

  String _formatDate(DateTime date) {
    // 只显示日期，不显示时间（因为文章发布时间通常只精确到日期）
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _shareArticle() async {
    try {
      // 构建分享内容
      final shareContent = _buildShareContent();

      // 复制到剪贴板
      await Clipboard.setData(ClipboardData(text: shareContent));

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('分享内容已复制到剪贴板'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('复制失败: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 构建分享内容
  String _buildShareContent() {
    final buffer = StringBuffer();

    // 添加文章标题
    // buffer.writeln(widget.article.title);

    // 添加文章URL
    buffer.writeln(widget.article.url);

    // 检查并添加视频地址
    final videoUrls = _collectVideoUrls();
    if (videoUrls.isNotEmpty) {
      for (final videoUrl in videoUrls) {
        buffer.writeln(videoUrl);
      }
    }

    return buffer.toString().trim();
  }

  /// 收集所有视频URL
  List<String> _collectVideoUrls() {
    final videoUrls = <String>[];
    final contentBlocks = _articleWithContent?.contentBlocks;

    if (contentBlocks != null) {
      _collectVideoUrlsFromBlocks(contentBlocks, videoUrls);
    }

    return videoUrls;
  }

  /// 递归收集视频URL
  void _collectVideoUrlsFromBlocks(List<ContentBlock> blocks, List<String> videoUrls) {
    for (final block in blocks) {
      if (block is VideoBlock && block.src.isNotEmpty) {
        videoUrls.add(block.src);
      } else if (block is EmbedBlock && block.src.isNotEmpty) {
        // 检查嵌入内容是否为视频
        final src = block.src.toLowerCase();
        if (src.contains('video') ||
            src.contains('youtube') ||
            src.contains('bilibili') ||
            src.contains('vimeo') ||
            src.contains('.mp4') ||
            src.contains('.m3u8')) {
          videoUrls.add(block.src);
        }
      } else if (block is MixedBlock) {
        _collectVideoUrlsFromBlocks(block.children, videoUrls);
      } else if (block is BlockquoteBlock) {
        _collectVideoUrlsFromBlocks(block.children, videoUrls);
      } else if (block is ListItemBlock) {
        _collectVideoUrlsFromBlocks(block.children, videoUrls);
      }
    }
  }

}
