#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的内容解析器
集成视频URL提取功能，为Flutter应用提供完整的内容解析
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, unquote
from html_analyzer import HTMLAnalyzer
from video_url_extractor import VideoURLExtractor

class EnhancedContentParser:
    def __init__(self):
        self.base_url = "https://agree.blinkit.top"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.html_analyzer = HTMLAnalyzer()
        self.video_extractor = VideoURLExtractor()
    
    def parse_article_content(self, article_url):
        """解析文章内容，返回Flutter ContentBlock格式"""
        print(f"🔍 解析文章内容: {article_url}")
        
        try:
            # 获取HTML内容
            response = requests.get(article_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            html_content = response.text
            
            # 提取视频URL
            video_result = self.video_extractor.extract_video_urls(article_url)
            
            # 分析媒体内容
            soup = BeautifulSoup(html_content, 'html.parser')
            media_analysis = self.html_analyzer.analyze_media_content(soup)
            
            # 解析文章内容区域
            content_blocks = self._parse_content_to_blocks(soup, video_result, media_analysis)
            
            result = {
                'url': article_url,
                'content_blocks': content_blocks,
                'video_urls': video_result['video_urls'] if video_result else [],
                'media_summary': {
                    'videos': len(video_result['video_urls']) if video_result else 0,
                    'images': len(media_analysis['images']),
                    'audio': len(media_analysis['audio']),
                    'embeds': len(media_analysis['embeds'])
                }
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 解析失败: {e}")
            return None
    
    def _parse_content_to_blocks(self, soup, video_result, media_analysis):
        """将HTML内容解析为ContentBlock格式"""
        content_blocks = []
        
        # 查找主要内容区域
        content_selectors = [
            '.post-content',
            '.entry-content', 
            '.article-content',
            '.content',
            'article',
            '.post',
            '.entry'
        ]
        
        content_element = None
        for selector in content_selectors:
            content_element = soup.select_one(selector)
            if content_element:
                break
        
        if not content_element:
            content_element = soup.find('body')
        
        if not content_element:
            return content_blocks
        
        # 首先添加视频块
        if video_result and video_result['video_urls']:
            for video in video_result['video_urls']:
                video_block = {
                    'type': 'video',
                    'src': video['url'],
                    'platform': self._detect_video_platform(video['url']),
                    'controls': True,
                    'autoplay': False,
                    'format': video['format'],
                    'video_type': video['type']
                }
                content_blocks.append(video_block)
        
        # 解析文本内容
        for element in content_element.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div']):
            text = element.get_text().strip()
            if not text:
                continue
            
            if element.name.startswith('h'):
                # 标题块
                level = int(element.name[1])
                content_blocks.append({
                    'type': 'heading',
                    'text': text,
                    'level': level
                })
            else:
                # 段落块
                content_blocks.append({
                    'type': 'paragraph',
                    'text': text
                })
        
        # 添加图片块（排除占位符图片）
        for image in media_analysis['images']:
            if 'zw.png' not in image['src']:  # 排除占位符
                image_block = {
                    'type': 'image',
                    'src': image['src'],
                    'alt': image.get('alt', ''),
                    'title': image.get('title', '')
                }
                content_blocks.append(image_block)
        
        # 添加音频块
        for audio in media_analysis['audio']:
            audio_block = {
                'type': 'audio',
                'src': audio['src'],
                'controls': audio.get('controls', True),
                'autoplay': audio.get('autoplay', False)
            }
            content_blocks.append(audio_block)
        
        # 添加嵌入内容块
        for embed in media_analysis['embeds']:
            embed_block = {
                'type': 'embed',
                'src': embed.get('src', embed.get('data', '')),
                'embedType': embed['type'],
                'width': embed.get('width'),
                'height': embed.get('height')
            }
            content_blocks.append(embed_block)
        
        return content_blocks
    
    def _detect_video_platform(self, url):
        """检测视频平台"""
        url_lower = url.lower()
        
        if 'youtube.com' in url_lower or 'youtu.be' in url_lower:
            return 'youtube'
        elif 'bilibili.com' in url_lower or 'b23.tv' in url_lower:
            return 'bilibili'
        elif 'vimeo.com' in url_lower:
            return 'vimeo'
        elif 'hls.qzkj.tech' in url_lower:
            return 'qzkj'
        elif '.m3u8' in url_lower:
            return 'hls'
        elif '.mp4' in url_lower:
            return 'mp4'
        else:
            return 'unknown'
    
    def generate_dart_content_blocks(self, content_blocks):
        """生成Dart代码格式的ContentBlock列表"""
        dart_blocks = []
        
        for block in content_blocks:
            if block['type'] == 'video':
                dart_code = f"""VideoBlock(
  src: '{block['src']}',
  platform: '{block.get('platform', 'unknown')}',
  controls: {str(block.get('controls', True)).lower()},
  autoplay: {str(block.get('autoplay', False)).lower()},
)"""
                dart_blocks.append(dart_code)
            
            elif block['type'] == 'paragraph':
                dart_code = f"""ParagraphBlock(
  text: '{block['text'].replace("'", "\\'")}',
)"""
                dart_blocks.append(dart_code)
            
            elif block['type'] == 'heading':
                dart_code = f"""HeadingBlock(
  text: '{block['text'].replace("'", "\\'")}',
  level: HeadingLevel.h{block['level']},
)"""
                dart_blocks.append(dart_code)
            
            elif block['type'] == 'image':
                dart_code = f"""ImageBlock(
  src: '{block['src']}',
  alt: '{block.get('alt', '').replace("'", "\\'")}',
  title: '{block.get('title', '').replace("'", "\\'")}',
)"""
                dart_blocks.append(dart_code)
            
            elif block['type'] == 'audio':
                dart_code = f"""AudioBlock(
  src: '{block['src']}',
  controls: {str(block.get('controls', True)).lower()},
  autoplay: {str(block.get('autoplay', False)).lower()},
)"""
                dart_blocks.append(dart_code)
            
            elif block['type'] == 'embed':
                dart_code = f"""EmbedBlock(
  src: '{block['src']}',
  embedType: '{block.get('embedType', 'iframe')}',
  width: {block.get('width', 'null')},
  height: {block.get('height', 'null')},
)"""
                dart_blocks.append(dart_code)
        
        return dart_blocks
    
    def save_flutter_integration_example(self, article_url, content_blocks):
        """保存Flutter集成示例"""
        dart_blocks = self.generate_dart_content_blocks(content_blocks)
        
        flutter_code = f"""// Flutter ContentBlock 集成示例
// 文章URL: {article_url}

import 'package:flutter/material.dart';
import '../models/content_block.dart';
import '../widgets/content_block_renderer.dart';

class ArticleContentExample extends StatelessWidget {{
  @override
  Widget build(BuildContext context) {{
    final contentBlocks = <ContentBlock>[
{chr(10).join('      ' + block + ',' for block in dart_blocks)}
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contentBlocks
          .map((block) => ContentBlockRenderer(block: block))
          .toList(),
    );
  }}
}}
"""
        
        filename = f"flutter_integration_example_{article_url.split('/')[-2]}.dart"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(flutter_code)
        
        print(f"💾 Flutter集成示例已保存到: {filename}")

def main():
    parser = EnhancedContentParser()
    
    # 测试URL
    test_url = "https://agree.blinkit.top/archives/214691/"
    
    print(f"🚀 增强内容解析器测试")
    print(f"目标URL: {test_url}")
    print("="*80)
    
    # 解析内容
    result = parser.parse_article_content(test_url)
    
    if result:
        print(f"\n📊 解析结果:")
        print(f"  内容块数量: {len(result['content_blocks'])}")
        print(f"  视频数量: {result['media_summary']['videos']}")
        print(f"  图片数量: {result['media_summary']['images']}")
        print(f"  音频数量: {result['media_summary']['audio']}")
        print(f"  嵌入内容数量: {result['media_summary']['embeds']}")
        
        # 显示内容块详情
        print(f"\n📝 内容块详情:")
        for i, block in enumerate(result['content_blocks'], 1):
            print(f"  {i:2d}. {block['type']}")
            if block['type'] == 'video':
                print(f"      URL: {block['src']}")
                print(f"      平台: {block.get('platform', 'unknown')}")
            elif block['type'] in ['paragraph', 'heading']:
                text_preview = block['text'][:50] + '...' if len(block['text']) > 50 else block['text']
                print(f"      文本: {text_preview}")
            elif block['type'] == 'image':
                print(f"      URL: {block['src']}")
        
        # 保存结果
        with open('enhanced_parsing_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 详细结果已保存到: enhanced_parsing_result.json")
        
        # 生成Flutter集成示例
        parser.save_flutter_integration_example(test_url, result['content_blocks'])
        
    else:
        print("❌ 解析失败")

if __name__ == '__main__':
    main()
