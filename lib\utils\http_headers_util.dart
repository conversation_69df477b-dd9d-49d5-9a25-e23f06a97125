/// HTTP Headers 工具类
/// 提供统一的HTTP请求头配置，避免重复代码
class HttpHeadersUtil {
  /// 标准的User-Agent字符串
  static const String _userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
  
  /// 获取标准的HTTP请求头
  /// 包含User-Agent、Accept、Accept-Language等常用头部
  static Map<String, String> getStandardHeaders() {
    return {
      'User-Agent': _userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    };
  }
  
  /// 获取简化的HTTP请求头
  /// 只包含User-Agent、Accept、Accept-Language
  static Map<String, String> getBasicHeaders() {
    return {
      'User-Agent': _userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    };
  }
  
  /// 获取仅包含User-Agent的请求头
  static Map<String, String> getUserAgentHeader() {
    return {
      'User-Agent': _userAgent,
    };
  }
  
  /// 获取自定义User-Agent的请求头
  static Map<String, String> getCustomHeaders({
    String? userAgent,
    String? accept,
    String? acceptLanguage,
    Map<String, String>? additionalHeaders,
  }) {
    final headers = <String, String>{
      'User-Agent': userAgent ?? _userAgent,
    };
    
    if (accept != null) {
      headers['Accept'] = accept;
    }
    
    if (acceptLanguage != null) {
      headers['Accept-Language'] = acceptLanguage;
    }
    
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }
    
    return headers;
  }
  
  /// 获取当前使用的User-Agent字符串
  static String get userAgent => _userAgent;
}
