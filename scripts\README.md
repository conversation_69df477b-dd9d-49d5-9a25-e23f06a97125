# HTML分析脚本使用说明

这个目录包含了用于分析网页HTML源码并提取媒体内容的脚本工具。

## 文件说明

### html_analyzer.py
主要的HTML分析器，用于：
- 获取网页HTML源码
- 分析页面中的视频、音频、图片等媒体内容
- 提取嵌入内容（iframe等）
- 分析JavaScript中的媒体链接

### test_analyzer.py
测试脚本，用于：
- 测试HTML分析器的功能
- 分析示例URL
- 分析本地HTML文件

## 使用方法

### 1. 安装依赖
```bash
pip install requests beautifulsoup4
```

### 2. 分析网页URL
```bash
python html_analyzer.py "https://agree.blinkit.top/archives/12345/" -o result.json -v
```

参数说明：
- `url`: 要分析的网页URL
- `-o, --output`: 输出文件路径（JSON格式）
- `-v, --verbose`: 详细输出

### 3. 运行测试
```bash
python test_analyzer.py
```

## 分析结果

分析器会返回以下类型的媒体内容：

### 视频 (videos)
- `type`: 视频类型（video_tag, iframe_video, script_video）
- `src`: 视频源URL
- `poster`: 视频封面图片
- `platform`: 视频平台（youtube, bilibili, vimeo等）
- `width/height`: 尺寸信息
- `controls/autoplay/loop/muted`: 播放控制属性

### 图片 (images)
- `src`: 图片URL
- `alt`: 替代文本
- `title`: 标题
- `width/height`: 尺寸信息
- `data_src/data_original`: 懒加载图片URL

### 音频 (audio)
- `src`: 音频源URL
- `controls/autoplay/loop/muted`: 播放控制属性
- `sources`: 多个音频源

### 嵌入内容 (embeds)
- `type`: 嵌入类型（embed, object）
- `src/data`: 内容URL
- `width/height`: 尺寸信息
- `mime_type`: MIME类型

### 脚本信息 (scripts)
- `src`: 脚本URL
- `type`: 脚本类型
- `has_video_keywords`: 是否包含视频相关关键词

### 元信息 (meta_info)
- `title`: 页面标题
- 各种meta标签信息

## Flutter集成

分析结果可以用于扩展Flutter应用中的内容解析：

1. **ContentBlock模型已扩展**：
   - 添加了 `VideoBlock`、`AudioBlock`、`EmbedBlock`
   - 支持视频平台识别和属性设置

2. **ContentBlockRenderer已更新**：
   - 支持渲染视频、音频和嵌入内容
   - 提供点击处理和占位符显示

3. **使用示例**：
```dart
// 创建视频块
final videoBlock = VideoBlock(
  src: 'https://example.com/video.mp4',
  poster: 'https://example.com/poster.jpg',
  platform: 'youtube',
  controls: true,
);

// 渲染视频块
ContentBlockRenderer(block: videoBlock)
```

## 注意事项

1. **网络请求**：脚本会发送HTTP请求获取页面内容，请确保网络连接正常
2. **反爬虫**：某些网站可能有反爬虫机制，可能需要调整请求头或添加延时
3. **视频平台**：不同视频平台的嵌入方式不同，脚本会尝试识别常见平台
4. **JavaScript内容**：动态加载的内容可能无法通过静态HTML分析获取

## 扩展功能

可以根据需要扩展以下功能：
1. 支持更多视频平台
2. 添加音频格式识别
3. 提取视频时长信息
4. 支持直播流检测
5. 添加内容去重功能

## 故障排除

1. **无法获取页面内容**：检查URL是否正确，网络是否正常
2. **解析结果为空**：页面可能使用JavaScript动态加载内容
3. **视频无法播放**：检查视频URL是否有效，是否需要特殊权限

## 更新日志

- v1.0: 初始版本，支持基本的媒体内容分析
- 支持视频、音频、图片、嵌入内容的提取
- 集成到Flutter ContentBlock系统
