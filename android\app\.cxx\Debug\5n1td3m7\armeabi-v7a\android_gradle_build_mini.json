{"buildFiles": ["D:\\Dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_chi_gua\\android\\app\\.cxx\\Debug\\5n1td3m7\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_chi_gua\\android\\app\\.cxx\\Debug\\5n1td3m7\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}