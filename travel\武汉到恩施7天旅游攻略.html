<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>武汉到恩施7天深度旅游攻略</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .overview-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .overview-item {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .overview-item h3 {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .overview-item p {
            font-size: 1.3rem;
            font-weight: bold;
        }

        .day-card {
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .day-card:hover {
            transform: translateY(-5px);
        }

        .day-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .day-header h2 {
            font-size: 1.5rem;
        }

        .toggle-icon {
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }

        .day-content {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }

        .day-content.active {
            padding: 20px;
            max-height: 1000px;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #4facfe, #00f2fe);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 20px;
            width: 12px;
            height: 12px;
            background: #4facfe;
            border-radius: 50%;
            border: 3px solid white;
        }

        .attraction-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .attraction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .attraction-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stars {
            color: #ffd700;
        }

        .attraction-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-icon {
            font-size: 1.2rem;
        }

        .price-tag {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .free-tag {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .calculator-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .calculator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .calc-input-group {
            margin-bottom: 20px;
        }

        .calc-input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .calc-input-group input, .calc-input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .calc-input-group input:focus, .calc-input-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .calc-result {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .calc-result h3 {
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .total-price {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .price-breakdown {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 15px;
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .map-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .route-map {
            width: 100%;
            min-height: 500px;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .route-map::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .route-container {
            position: relative;
            z-index: 2;
        }

        .route-title {
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .route-flow {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .route-day {
            display: flex;
            align-items: center;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .route-day:hover {
            transform: translateX(10px);
        }

        .day-number {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .day-info {
            flex: 1;
        }

        .day-location {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .day-distance {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .day-attractions {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .route-arrow {
            text-align: center;
            font-size: 2rem;
            margin: 10px 0;
            opacity: 0.7;
        }

        .route-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .tips-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .tip-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .tip-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .overview-grid {
                grid-template-columns: 1fr;
            }
            
            .calculator-grid {
                grid-template-columns: 1fr;
            }
            
            .attraction-info {
                grid-template-columns: 1fr;
            }
            
            .price-breakdown {
                grid-template-columns: 1fr;
            }
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 25px 0;
            padding: 0;
            width: 100%;
            box-sizing: border-box;
        }

        .image-placeholder {
            min-height: 180px;
            height: 180px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            text-align: center;
            padding: 15px;
            box-sizing: border-box;
        }

        .image-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-placeholder:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .image-placeholder:hover::before {
            opacity: 1;
        }

        .image-placeholder:active {
            transform: translateY(-2px) scale(1.01);
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            .image-gallery {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }

            .image-placeholder {
                min-height: 150px;
                height: 150px;
                font-size: 1rem;
                padding: 12px;
            }
        }

        @media (max-width: 480px) {
            .image-gallery {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 12px;
                margin: 15px 0;
            }

            .image-placeholder {
                min-height: 120px;
                height: 120px;
                font-size: 0.9rem;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🌟 武汉到恩施7天深度旅游攻略</h1>
            <p>探索土家族文化，领略峡谷风光，享受宽松舒适的深度之旅</p>
        </header>

        <div class="overview-card">
            <h2>🗺️ 行程概览</h2>
            <div class="overview-grid">
                <div class="overview-item">
                    <h3>📅 总行程</h3>
                    <p>7天6晚</p>
                </div>
                <div class="overview-item">
                    <h3>🚗 总距离</h3>
                    <p>522公里</p>
                </div>
                <div class="overview-item">
                    <h3>🎫 门票总计</h3>
                    <p>618元/人</p>
                </div>
                <div class="overview-item">
                    <h3>💰 预算范围</h3>
                    <p>3018-4393元/人</p>
                </div>
            </div>
        </div>

        <!-- 第1天 -->
        <div class="day-card">
            <div class="day-header" onclick="toggleDay(1)">
                <h2>第1天：武汉 → 恩施市区（休息适应日）</h2>
                <span class="toggle-icon" id="icon-1">▼</span>
            </div>
            <div class="day-content" id="content-1">
                <div class="timeline">
                    <div class="timeline-item">
                        <strong>07:00</strong> 武汉出发（建议早起避开高峰）
                    </div>
                    <div class="timeline-item">
                        <strong>14:00</strong> 抵达恩施，入住酒店
                    </div>
                    <div class="timeline-item">
                        <strong>15:00-17:00</strong> 酒店休息，适应环境
                    </div>
                    <div class="timeline-item">
                        <strong>17:30-19:30</strong> 恩施市区简单游览，熟悉环境
                    </div>
                    <div class="timeline-item">
                        <strong>19:30-21:00</strong> 品尝恩施特色晚餐
                    </div>
                </div>
                
                <h3>🏨 住宿推荐</h3>
                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">恩施国际大酒店</span>
                        <span class="price-tag">350-450元/晚</span>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">⭐</span>
                            <span>4星级酒店</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">📍</span>
                            <span>沿江路264-9号</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第2天 -->
        <div class="day-card">
            <div class="day-header" onclick="toggleDay(2)">
                <h2>第2天：恩施市区文化游</h2>
                <span class="toggle-icon" id="icon-2">▼</span>
            </div>
            <div class="day-content" id="content-2">
                <div class="timeline">
                    <div class="timeline-item">
                        <strong>08:30-09:00</strong> 酒店早餐
                    </div>
                    <div class="timeline-item">
                        <strong>09:30-12:00</strong> 游览恩施土司城
                    </div>
                    <div class="timeline-item">
                        <strong>12:00-13:30</strong> 午餐休息
                    </div>
                    <div class="timeline-item">
                        <strong>14:00-17:00</strong> 自由活动，市区购物
                    </div>
                    <div class="timeline-item">
                        <strong>17:30-21:00</strong> 游览土家女儿城，观看民族表演
                    </div>
                </div>

                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">恩施土司城</span>
                        <div class="rating">
                            <span class="stars">⭐⭐⭐⭐</span>
                            <span>4.5分</span>
                        </div>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">🎫</span>
                            <span class="price-tag">45元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">📍</span>
                            <span>恩施市区内</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🕐</span>
                            <span>07:30-18:00</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🎯</span>
                            <span>土司文化、民族建筑</span>
                        </div>
                    </div>
                </div>

                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">土家女儿城</span>
                        <div class="rating">
                            <span class="stars">⭐⭐⭐⭐</span>
                            <span>4.5分</span>
                        </div>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">🎫</span>
                            <span class="free-tag">免费</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🚗</span>
                            <span>距市区6.6公里</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🕐</span>
                            <span>24小时开放</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🎯</span>
                            <span>夜景、美食街、民族表演</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第3天到第7天的内容 -->
        <div class="day-card">
            <div class="day-header" onclick="toggleDay(3)">
                <h2>第3天：恩施大峡谷（重点游览日）</h2>
                <span class="toggle-icon" id="icon-3">▼</span>
            </div>
            <div class="day-content" id="content-3">
                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">恩施大峡谷</span>
                        <div class="rating">
                            <span class="stars">⭐⭐⭐⭐⭐</span>
                            <span>4.4分</span>
                        </div>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">🎫</span>
                            <span class="price-tag">200元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🚗</span>
                            <span>距恩施48.8公里（1小时17分钟）</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">⏰</span>
                            <span>6-8小时游览</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🎯</span>
                            <span>七星寨、云龙地缝、一炷香</span>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🌟 游玩亮点</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #e74c3c; margin-bottom: 10px;">📸 必看景观和拍照点</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>一炷香</strong>：高约150米的石柱，恩施大峡谷标志性景观</li>
                                <li><strong>大楼门观景台</strong>：俯瞰整个峡谷的最佳位置</li>
                                <li><strong>母子情深</strong>：形似母子相拥的奇特石峰</li>
                                <li><strong>云龙地缝</strong>：深达百米的地下峡谷，瀑布飞流</li>
                            </ul>
                        </div>

                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #27ae60; margin-bottom: 10px;">🗺️ 游览路线建议</h5>
                            <p style="line-height: 1.8;">
                                <strong>推荐路线：</strong>游客中心 → 乘景交车 → 七星寨景区入口 → 小楼门 → 中楼门 → 大楼门观景台 → 一炷香 → 索道下山 → 云龙地缝 → 返回游客中心
                            </p>
                            <p style="line-height: 1.8; margin-top: 10px;">
                                <strong>时间安排：</strong>七星寨（4-5小时）+ 云龙地缝（2-3小时）
                            </p>
                        </div>

                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #856404; margin-bottom: 10px;">🎪 特色体验项目</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>高空索道</strong>：体验峡谷上空的刺激与美景</li>
                                <li><strong>悬崖栈道</strong>：在绝壁上行走，感受惊险刺激</li>
                                <li><strong>地缝探秘</strong>：深入地下峡谷，观赏瀑布奇观</li>
                                <li><strong>土家表演</strong>：欣赏原生态的土家族歌舞</li>
                            </ul>
                        </div>

                        <div style="background: #f8d7da; padding: 15px; border-radius: 8px;">
                            <h5 style="color: #721c24; margin-bottom: 10px;">⚠️ 注意事项和游玩技巧</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li>穿着舒适的运动鞋，避免高跟鞋</li>
                                <li>携带足够的水和食物，景区内餐饮较贵</li>
                                <li>建议购买索道票，可节省体力和时间</li>
                                <li>雨天路滑，注意安全，建议携带雨衣</li>
                                <li>最佳拍照时间：上午9-11点，下午3-5点光线最佳</li>
                            </ul>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        <div class="day-card">
            <div class="day-header" onclick="toggleDay(4)">
                <h2>第4天：腾龙洞探秘之旅</h2>
                <span class="toggle-icon" id="icon-4">▼</span>
            </div>
            <div class="day-content" id="content-4">
                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">腾龙洞</span>
                        <div class="rating">
                            <span class="stars">⭐⭐⭐⭐⭐</span>
                            <span>4.5分</span>
                        </div>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">🎫</span>
                            <span class="price-tag">150元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🚗</span>
                            <span>距恩施75.7公里（1小时10分钟）</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">⏰</span>
                            <span>4-5小时游览</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🎯</span>
                            <span>世界特级洞穴、地下暗河</span>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🌟 游玩亮点</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #e74c3c; margin-bottom: 10px;">📸 必看景观和拍照点</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>洞口大厅</strong>：高74米、宽64米的巨大洞口，气势磅礴</li>
                                <li><strong>激光秀表演</strong>：洞内大型激光音乐表演，震撼视听</li>
                                <li><strong>地下暗河</strong>：乘船游览神秘的地下河流</li>
                                <li><strong>钟乳石群</strong>：千姿百态的钟乳石造型</li>
                            </ul>
                        </div>

                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #27ae60; margin-bottom: 10px;">🗺️ 游览路线建议</h5>
                            <p style="line-height: 1.8;">
                                <strong>推荐路线：</strong>游客中心 → 洞口大厅 → 观看激光秀 → 旱洞游览 → 地下暗河乘船 → 水洞探秘 → 土家表演 → 返回出口
                            </p>
                            <p style="line-height: 1.8; margin-top: 10px;">
                                <strong>表演时间：</strong>激光秀每天多场，土家歌舞表演约30分钟
                            </p>
                        </div>

                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #856404; margin-bottom: 10px;">🎪 特色体验项目</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>激光音乐秀</strong>：洞内大型多媒体表演，视觉盛宴</li>
                                <li><strong>地下河漂流</strong>：乘船穿越神秘的地下暗河</li>
                                <li><strong>土家歌舞</strong>：原生态的土家族文化表演</li>
                                <li><strong>洞穴探险</strong>：深入洞穴深处，感受地质奇观</li>
                            </ul>
                        </div>

                        <div style="background: #f8d7da; padding: 15px; border-radius: 8px;">
                            <h5 style="color: #721c24; margin-bottom: 10px;">⚠️ 注意事项和游玩技巧</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li>洞内温度较低（约18°C），建议携带外套</li>
                                <li>洞内湿度大，注意防滑，穿防滑鞋</li>
                                <li>提前了解表演时间，合理安排游览顺序</li>
                                <li>拍照时注意保护设备，避免水汽影响</li>
                                <li>跟随导游游览，不要脱离团队</li>
                            </ul>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        <div class="day-card">
            <div class="day-header" onclick="toggleDay(5)">
                <h2>第5天：梭布垭石林奇观</h2>
                <span class="toggle-icon" id="icon-5">▼</span>
            </div>
            <div class="day-content" id="content-5">
                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">梭布垭石林景区</span>
                        <div class="rating">
                            <span class="stars">⭐⭐⭐⭐</span>
                            <span>4.5分</span>
                        </div>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">🎫</span>
                            <span class="price-tag">78元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🚗</span>
                            <span>距恩施60.4公里（1小时6分钟）</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">⏰</span>
                            <span>3-4小时游览</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🎯</span>
                            <span>奇峰异石、莲花寨、铁甲寨</span>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🌟 游玩亮点</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #e74c3c; margin-bottom: 10px;">📸 必看景观和拍照点</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>莲花寨</strong>：形似莲花的石林群，最具代表性景观</li>
                                <li><strong>铁甲寨</strong>：如铁甲武士般的石峰群落</li>
                                <li><strong>拇指山</strong>：形似竖起的拇指，寓意点赞</li>
                                <li><strong>石林迷宫</strong>：错综复杂的石林通道，充满探险乐趣</li>
                            </ul>
                        </div>

                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #27ae60; margin-bottom: 10px;">🗺️ 游览路线建议</h5>
                            <p style="line-height: 1.8;">
                                <strong>推荐路线：</strong>游客中心 → 乘观光车 → 莲花寨入口 → 莲花寨游览 → 铁甲寨广场 → 石林迷宫 → 拇指山观景台 → 返回游客中心
                            </p>
                            <p style="line-height: 1.8; margin-top: 10px;">
                                <strong>最佳时间：</strong>上午光线较好，适合拍照；下午可体验石林探险
                            </p>
                        </div>

                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #856404; margin-bottom: 10px;">🎪 特色体验项目</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>石林探险</strong>：在石林迷宫中寻找出路，体验探险乐趣</li>
                                <li><strong>观光车游览</strong>：轻松游览各个景点，节省体力</li>
                                <li><strong>地质科普</strong>：了解喀斯特地貌的形成过程</li>
                                <li><strong>摄影创作</strong>：独特的石林景观是摄影爱好者的天堂</li>
                            </ul>
                        </div>

                        <div style="background: #f8d7da; padding: 15px; border-radius: 8px;">
                            <h5 style="color: #721c24; margin-bottom: 10px;">⚠️ 注意事项和游玩技巧</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li>石林路面不平，建议穿舒适的运动鞋</li>
                                <li>部分路段较陡，注意安全，扶好栏杆</li>
                                <li>携带充足的水，景区内补给点较少</li>
                                <li>注意防晒，石林地区紫外线较强</li>
                                <li>保护环境，不要攀爬石峰或乱扔垃圾</li>
                            </ul>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        <div class="day-card">
            <div class="day-header" onclick="toggleDay(6)">
                <h2>第6天：清江画廊山水之旅</h2>
                <span class="toggle-icon" id="icon-6">▼</span>
            </div>
            <div class="day-content" id="content-6">
                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">清江画廊旅游度假区</span>
                        <div class="rating">
                            <span class="stars">⭐⭐⭐⭐⭐</span>
                            <span>4.5分</span>
                        </div>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">🎫</span>
                            <span class="price-tag">145元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🚗</span>
                            <span>距恩施222.9公里（3小时16分钟）</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">⏰</span>
                            <span>4-5小时游览</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">🎯</span>
                            <span>清江山水、游船观光</span>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🌟 游玩亮点</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #e74c3c; margin-bottom: 10px;">📸 必看景观和拍照点</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>武落钟离山</strong>：清江画廊的标志性景观，形似巨人</li>
                                <li><strong>倒影峡</strong>：山水倒影，如诗如画的绝美景色</li>
                                <li><strong>清江大佛</strong>：巨型石刻佛像，气势恢宏</li>
                                <li><strong>土家风雨廊桥</strong>：传统建筑与自然风光的完美结合</li>
                            </ul>
                        </div>

                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #27ae60; margin-bottom: 10px;">🗺️ 游览路线建议</h5>
                            <p style="line-height: 1.8;">
                                <strong>推荐路线：</strong>游客中心 → 乘船游览 → 倒影峡 → 武落钟离山 → 清江大佛 → 土家风雨廊桥 → 返回码头
                            </p>
                            <p style="line-height: 1.8; margin-top: 10px;">
                                <strong>游船时间：</strong>约2-3小时，建议上午乘船，光线最佳
                            </p>
                        </div>

                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <h5 style="color: #856404; margin-bottom: 10px;">🎪 特色体验项目</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li><strong>游船观光</strong>：乘船欣赏两岸峰林和清江碧水</li>
                                <li><strong>花咚咚的姐表演</strong>：土家族特色歌舞表演</li>
                                <li><strong>峡谷漂流</strong>：夏季可体验刺激的峡谷漂流</li>
                                <li><strong>土家美食</strong>：品尝当地特色美食，如清江鱼</li>
                            </ul>
                        </div>

                        <div style="background: #f8d7da; padding: 15px; border-radius: 8px;">
                            <h5 style="color: #721c24; margin-bottom: 10px;">⚠️ 注意事项和游玩技巧</h5>
                            <ul style="margin-left: 20px; line-height: 1.8;">
                                <li>提前了解船班时间，避开高峰期</li>
                                <li>船上风大，建议携带外套</li>
                                <li>准备防晒用品，水面反光强烈</li>
                                <li>带好相机，江面景色非常适合拍照</li>
                                <li>游览结束后可直接前往宜昌市区，不必返回恩施</li>
                            </ul>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        <div class="day-card">
            <div class="day-header" onclick="toggleDay(7)">
                <h2>第7天：宜昌 → 武汉（返程日）</h2>
                <span class="toggle-icon" id="icon-7">▼</span>
            </div>
            <div class="day-content" id="content-7">
                <div class="attraction-card">
                    <div class="attraction-header">
                        <span class="attraction-title">返程路线</span>
                    </div>
                    <div class="attraction-info">
                        <div class="info-item">
                            <span class="info-icon">🚗</span>
                            <span>宜昌到武汉约300公里</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">⏰</span>
                            <span>约3.5小时车程</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 费用计算器 -->
        <div class="calculator-section">
            <h2>💰 费用计算器</h2>
            <div class="calculator-grid">
                <div>
                    <div class="calc-input-group">
                        <label for="people">出行人数：</label>
                        <input type="number" id="people" value="2" min="1" max="10" onchange="calculateCost()">
                    </div>
                    <div class="calc-input-group">
                        <label for="hotel-level">酒店档次：</label>
                        <select id="hotel-level" onchange="calculateCost()">
                            <option value="300">经济型 (200-300元/晚)</option>
                            <option value="400" selected>中档型 (300-400元/晚)</option>
                            <option value="500">高档型 (400-500元/晚)</option>
                        </select>
                    </div>
                    <div class="calc-input-group">
                        <label for="meal-budget">餐饮预算：</label>
                        <select id="meal-budget" onchange="calculateCost()">
                            <option value="80">经济型 (80元/人/天)</option>
                            <option value="120" selected>中档型 (120元/人/天)</option>
                            <option value="180">高档型 (180元/人/天)</option>
                        </select>
                    </div>
                </div>
                <div class="calc-result">
                    <h3>💰 费用预算</h3>
                    <div class="total-price" id="total-price">6036元</div>
                    <div class="price-breakdown">
                        <div class="price-item">
                            <span>门票费用：</span>
                            <span id="ticket-cost">1236元</span>
                        </div>
                        <div class="price-item">
                            <span>住宿费用：</span>
                            <span id="hotel-cost">2400元</span>
                        </div>
                        <div class="price-item">
                            <span>交通费用：</span>
                            <span id="transport-cost">1800元</span>
                        </div>
                        <div class="price-item">
                            <span>餐饮费用：</span>
                            <span id="meal-cost">1680元</span>
                        </div>
                    </div>
                    <p style="margin-top: 15px; font-size: 1.1rem;">
                        人均：<span id="per-person" style="font-weight: bold;">3018元</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- 路线地图 -->
        <div class="map-section">
            <h2>🗺️ 旅游路线图</h2>
            <div class="route-map">
                <div class="route-container">
                    <div class="route-title">🚗 武汉 → 恩施 → 宜昌 → 武汉</div>

                    <div class="route-flow">
                        <div class="route-day">
                            <div class="day-number">1</div>
                            <div class="day-info">
                                <div class="day-location">武汉 → 恩施市区</div>
                                <div class="day-distance">🚗 522公里 | ⏱️ 约7小时</div>
                                <div class="day-attractions">休息适应，熟悉环境</div>
                            </div>
                        </div>

                        <div class="route-arrow">↓</div>

                        <div class="route-day">
                            <div class="day-number">2</div>
                            <div class="day-info">
                                <div class="day-location">恩施市区文化游</div>
                                <div class="day-distance">🏛️ 恩施土司城 | 🏮 土家女儿城</div>
                                <div class="day-attractions">土司文化、民族表演、特色美食</div>
                            </div>
                        </div>

                        <div class="route-arrow">↓</div>

                        <div class="route-day">
                            <div class="day-number">3</div>
                            <div class="day-info">
                                <div class="day-location">恩施大峡谷</div>
                                <div class="day-distance">🚗 48.8公里 | ⏱️ 约1小时17分钟</div>
                                <div class="day-attractions">七星寨、云龙地缝、一炷香</div>
                            </div>
                        </div>

                        <div class="route-arrow">↓</div>

                        <div class="route-day">
                            <div class="day-number">4</div>
                            <div class="day-info">
                                <div class="day-location">腾龙洞</div>
                                <div class="day-distance">🚗 75.7公里 | ⏱️ 约1小时10分钟</div>
                                <div class="day-attractions">世界特级洞穴、地下暗河、激光秀</div>
                            </div>
                        </div>

                        <div class="route-arrow">↓</div>

                        <div class="route-day">
                            <div class="day-number">5</div>
                            <div class="day-info">
                                <div class="day-location">梭布垭石林</div>
                                <div class="day-distance">🚗 60.4公里 | ⏱️ 约1小时6分钟</div>
                                <div class="day-attractions">莲花寨、铁甲寨、石林迷宫</div>
                            </div>
                        </div>

                        <div class="route-arrow">↓</div>

                        <div class="route-day">
                            <div class="day-number">6</div>
                            <div class="day-info">
                                <div class="day-location">恩施 → 清江画廊 → 宜昌</div>
                                <div class="day-distance">🚗 222.9公里 | ⏱️ 约3小时16分钟</div>
                                <div class="day-attractions">清江山水、游船观光、武落钟离山</div>
                            </div>
                        </div>

                        <div class="route-arrow">↓</div>

                        <div class="route-day">
                            <div class="day-number">7</div>
                            <div class="day-info">
                                <div class="day-location">宜昌 → 武汉</div>
                                <div class="day-distance">🚗 约300公里 | ⏱️ 约3.5小时</div>
                                <div class="day-attractions">返程，结束愉快旅程</div>
                            </div>
                        </div>
                    </div>

                    <div class="route-stats">
                        <div class="stat-card">
                            <div class="stat-number">1600</div>
                            <div class="stat-label">总里程（公里）</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">7</div>
                            <div class="stat-label">行程天数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">6</div>
                            <div class="stat-label">景点数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">16</div>
                            <div class="stat-label">驾车时间（小时）</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 旅游贴士 -->
        <div class="tips-section">
            <h2>🎯 旅游贴士</h2>
            <div class="tips-grid">
                <div class="tip-card">
                    <div class="tip-icon">🌤️</div>
                    <h3>最佳季节</h3>
                    <p>4-10月气候宜人，山花烂漫，是最佳旅游时间</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">🚗</div>
                    <h3>交通建议</h3>
                    <p>自驾游最灵活，也可选择高铁+当地包车</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">🏨</div>
                    <h3>住宿建议</h3>
                    <p>建议选择恩施市区酒店，交通便利，餐饮丰富</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">🍜</div>
                    <h3>美食推荐</h3>
                    <p>土家三下锅、恩施玉露茶、腊肉炒饭、清江鱼</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">⚠️</div>
                    <h3>注意事项</h3>
                    <p>山区天气多变，备好雨具和保暖衣物</p>
                </div>
                <div class="tip-card">
                    <div class="tip-icon">🎒</div>
                    <h3>必备物品</h3>
                    <p>身份证、现金、充电宝、常用药品、舒适鞋子</p>
                </div>
            </div>
        </div>

        <footer style="text-align: center; padding: 30px; color: white;">
            <p>🌟 祝您旅途愉快！本攻略基于高德地图实时数据制作 🌟</p>
        </footer>
    </div>

    <script>
        function toggleDay(dayNumber) {
            const content = document.getElementById(`content-${dayNumber}`);
            const icon = document.getElementById(`icon-${dayNumber}`);

            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.textContent = '▼';
            } else {
                content.classList.add('active');
                icon.textContent = '▲';
            }
        }

        function calculateCost() {
            const people = parseInt(document.getElementById('people').value);
            const hotelLevel = parseInt(document.getElementById('hotel-level').value);
            const mealBudget = parseInt(document.getElementById('meal-budget').value);

            // 优化后的费用计算逻辑
            const ticketCost = 618 * people; // 门票：每人618元

            // 住宿费用：按房间计算，2人共享一间房
            const roomsNeeded = Math.ceil(people / 2); // 向上取整，确保房间够用
            const hotelCost = hotelLevel * 6 * roomsNeeded; // 6晚住宿 × 房间数

            // 交通费用：按人数分摊（自驾游油费过路费等）
            const baseTransportCost = 1800; // 基础交通费用
            const transportCost = people <= 4 ? baseTransportCost : baseTransportCost + (people - 4) * 200; // 超过4人需额外费用

            // 餐饮费用：每人每天的餐费
            const mealCost = mealBudget * people * 7; // 7天餐费

            const totalCost = ticketCost + hotelCost + transportCost + mealCost;
            const perPersonCost = Math.round(totalCost / people);

            // 更新显示
            document.getElementById('ticket-cost').textContent = ticketCost + '元';
            document.getElementById('hotel-cost').textContent = hotelCost + '元 (' + roomsNeeded + '间房)';
            document.getElementById('transport-cost').textContent = transportCost + '元';
            document.getElementById('meal-cost').textContent = mealCost + '元';
            document.getElementById('total-price').textContent = totalCost + '元';
            document.getElementById('per-person').textContent = perPersonCost + '元';
        }

        // 页面加载时计算一次
        window.onload = function() {
            calculateCost();
        };

        // 图片点击交互功能增强
        document.addEventListener('DOMContentLoaded', function() {
            const imagePlaceholders = document.querySelectorAll('.image-placeholder');

            imagePlaceholders.forEach((placeholder, index) => {
                // 添加点击事件
                placeholder.addEventListener('click', function() {
                    const scenicSpot = this.textContent;
                    const message = `🖼️ ${scenicSpot}\n\n📸 这里将展示高清图片\n🔍 点击可放大查看\n📱 支持手势缩放\n\n实际使用时可以集成图片轮播组件，支持：\n• 高清图片展示\n• 手势缩放和滑动\n• 图片说明和介绍\n• 分享功能`;

                    // 创建自定义弹窗效果
                    showImageModal(scenicSpot, message);
                });

                // 添加键盘支持
                placeholder.setAttribute('tabindex', '0');
                placeholder.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });

                // 添加无障碍支持
                placeholder.setAttribute('role', 'button');
                placeholder.setAttribute('aria-label', `查看${this.textContent}的图片`);
            });
        });

        // 自定义图片模态框函数
        function showImageModal(title, content) {
            // 创建模态框背景
            const modalBg = document.createElement('div');
            modalBg.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease;
            `;

            // 创建模态框内容
            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 15px;
                padding: 30px;
                max-width: 500px;
                max-height: 80vh;
                overflow-y: auto;
                margin: 20px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
                position: relative;
            `;

            modalContent.innerHTML = `
                <div style="text-align: center;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.5rem;">${title}</h3>
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); height: 200px; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem; margin-bottom: 20px;">
                        📷 图片预览区域
                    </div>
                    <p style="line-height: 1.6; color: #555; white-space: pre-line; text-align: left;">${content}</p>
                    <button onclick="closeImageModal()" style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-size: 1rem;
                        margin-top: 20px;
                        transition: transform 0.3s ease;
                    " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        关闭
                    </button>
                </div>
            `;

            modalBg.appendChild(modalContent);
            document.body.appendChild(modalBg);

            // 点击背景关闭
            modalBg.addEventListener('click', function(e) {
                if (e.target === modalBg) {
                    closeImageModal();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeImageModal();
                }
            });

            // 存储模态框引用
            window.currentModal = modalBg;
        }

        // 关闭图片模态框
        function closeImageModal() {
            if (window.currentModal) {
                window.currentModal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    if (window.currentModal && window.currentModal.parentNode) {
                        window.currentModal.parentNode.removeChild(window.currentModal);
                    }
                    window.currentModal = null;
                }, 300);
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
