# 链接处理功能演示

## 功能概述

本次更新实现了两个主要功能：

### 1. HTTP Headers 统一管理

**问题**：之前在多个服务中都有重复的User-Agent定义
**解决方案**：创建了`HttpHeadersUtil`工具类统一管理

#### 使用示例：

```dart
// 标准headers（包含所有常用头部）
final headers = HttpHeadersUtil.getStandardHeaders();

// 基础headers（只包含核心头部）
final basicHeaders = HttpHeadersUtil.getBasicHeaders();

// 仅User-Agent
final userAgentHeader = HttpHeadersUtil.getUserAgentHeader();

// 自定义headers
final customHeaders = HttpHeadersUtil.getCustomHeaders(
  userAgent: 'Custom User Agent',
  additionalHeaders: {'Custom-Header': 'Value'},
);
```

#### 更新的服务：
- `WebScrapingService`: 使用`getStandardHeaders()`
- `CategoryService`: 使用`getBasicHeaders()`和`getUserAgentHeader()`

### 2. 智能链接处理

**问题**：文章内容中的链接只能用外部浏览器打开，无法在应用内导航
**解决方案**：增强`ContentBlockRenderer`的链接处理逻辑

#### 功能特性：

1. **相对链接转换**：
   - 自动将相对链接（如`/archives/123`）转换为绝对链接
   - 使用`SettingsService.getFullUrl()`方法

2. **智能跳转判断**：
   - **包含tag参数的URL** → 跳转到分类列表页
   - **文章URL（包含/archives/）** → 跳转到文章详情页
   - **其他链接** → 使用外部浏览器打开

3. **压栈式导航**：
   - 使用`Navigator.push()`实现压栈导航
   - 用户可以通过返回按钮回到原页面

#### 链接类型识别：

```dart
// 分类/标签链接示例
https://example.com/category/tech/
https://example.com/?tag=news
https://example.com/tag/sports/

// 文章链接示例  
https://example.com/archives/123
https://example.com/post/456
https://example.com/article/789

// 相对链接示例
/archives/123 → https://baseurl.com/archives/123
category/tech/ → https://baseurl.com/category/tech/
```

## 测试验证

运行测试验证功能：
```bash
flutter test test/link_handling_test.dart
```

测试覆盖：
- ✅ HTTP Headers工具类各种方法
- ✅ User-Agent一致性
- ✅ 相对链接转绝对链接
- ✅ URL处理边界情况

## 使用场景

1. **文章详情页**：用户点击文章内的链接时，会根据链接类型智能跳转
2. **分类文章页**：同样支持智能链接处理
3. **网络请求**：所有HTTP请求现在使用统一的headers配置

## 代码改进

- 消除了重复的User-Agent定义
- 提高了代码可维护性
- 增强了用户体验（应用内导航）
- 添加了完整的错误处理
