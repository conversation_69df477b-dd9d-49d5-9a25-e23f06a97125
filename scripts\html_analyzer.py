#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML源码获取和分析脚本
用于分析详情页的HTML源码，提取视频和其他媒体信息
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import argparse
import sys
from typing import Dict, List, Optional, Any

class HTMLAnalyzer:
    def __init__(self):
        self.base_url = "https://agree.blinkit.top"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def fetch_html(self, url: str) -> Optional[str]:
        """获取页面HTML源码"""
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"获取HTML失败: {e}")
            return None
    
    def analyze_videos(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """分析页面中的视频元素"""
        videos = []
        
        # 查找 video 标签
        video_tags = soup.find_all('video')
        for video in video_tags:
            video_info = {
                'type': 'video_tag',
                'src': video.get('src'),
                'poster': video.get('poster'),
                'controls': video.has_attr('controls'),
                'autoplay': video.has_attr('autoplay'),
                'loop': video.has_attr('loop'),
                'muted': video.has_attr('muted'),
                'width': video.get('width'),
                'height': video.get('height'),
                'sources': []
            }
            
            # 查找 source 标签
            sources = video.find_all('source')
            for source in sources:
                video_info['sources'].append({
                    'src': source.get('src'),
                    'type': source.get('type')
                })
            
            videos.append(video_info)
        
        # 查找 iframe 中的视频（如YouTube, Bilibili等）
        iframes = soup.find_all('iframe')
        for iframe in iframes:
            src = iframe.get('src', '')
            if self._is_video_iframe(src):
                video_info = {
                    'type': 'iframe_video',
                    'src': src,
                    'width': iframe.get('width'),
                    'height': iframe.get('height'),
                    'platform': self._detect_video_platform(src)
                }
                videos.append(video_info)
        
        # 查找JavaScript中的视频链接
        script_videos = self._extract_videos_from_scripts(soup)
        videos.extend(script_videos)
        
        return videos
    
    def analyze_images(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """分析页面中的图片元素"""
        images = []
        
        img_tags = soup.find_all('img')
        for img in img_tags:
            src = img.get('src')
            if src:
                # 处理相对URL
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = urljoin(self.base_url, src)
                
                image_info = {
                    'src': src,
                    'alt': img.get('alt'),
                    'title': img.get('title'),
                    'width': img.get('width'),
                    'height': img.get('height'),
                    'class': img.get('class'),
                    'data_src': img.get('data-src'),  # 懒加载图片
                    'data_original': img.get('data-original')
                }
                images.append(image_info)
        
        return images
    
    def analyze_media_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """综合分析页面中的媒体内容"""
        # 查找主要内容区域
        content_selectors = [
            '.post-content',
            '.entry-content', 
            '.article-content',
            '.content',
            'article',
            '.post',
            '.entry'
        ]
        
        content_element = None
        for selector in content_selectors:
            content_element = soup.select_one(selector)
            if content_element:
                break
        
        if not content_element:
            content_element = soup.find('body')
        
        analysis_result = {
            'videos': self.analyze_videos(content_element or soup),
            'images': self.analyze_images(content_element or soup),
            'audio': self._analyze_audio(content_element or soup),
            'embeds': self._analyze_embeds(content_element or soup),
            'scripts': self._analyze_scripts(soup),
            'meta_info': self._extract_meta_info(soup)
        }
        
        return analysis_result
    
    def _is_video_iframe(self, src: str) -> bool:
        """判断iframe是否为视频"""
        video_domains = [
            'youtube.com', 'youtu.be',
            'bilibili.com', 'b23.tv',
            'vimeo.com',
            'dailymotion.com',
            'twitch.tv',
            'tiktok.com'
        ]
        
        for domain in video_domains:
            if domain in src:
                return True
        return False
    
    def _detect_video_platform(self, src: str) -> str:
        """检测视频平台"""
        if 'youtube.com' in src or 'youtu.be' in src:
            return 'youtube'
        elif 'bilibili.com' in src or 'b23.tv' in src:
            return 'bilibili'
        elif 'vimeo.com' in src:
            return 'vimeo'
        elif 'dailymotion.com' in src:
            return 'dailymotion'
        elif 'twitch.tv' in src:
            return 'twitch'
        elif 'tiktok.com' in src:
            return 'tiktok'
        else:
            return 'unknown'
    
    def _extract_videos_from_scripts(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """从JavaScript代码中提取视频链接"""
        videos = []
        scripts = soup.find_all('script')
        
        # 常见的视频URL模式
        video_patterns = [
            r'https?://[^"\s]+\.(?:mp4|webm|ogg|avi|mov|wmv|flv|m4v)',
            r'https?://[^"\s]*(?:youtube\.com|youtu\.be)[^"\s]*',
            r'https?://[^"\s]*bilibili\.com[^"\s]*',
            r'https?://[^"\s]*vimeo\.com[^"\s]*'
        ]
        
        for script in scripts:
            script_text = script.get_text()
            for pattern in video_patterns:
                matches = re.findall(pattern, script_text, re.IGNORECASE)
                for match in matches:
                    videos.append({
                        'type': 'script_video',
                        'src': match,
                        'platform': self._detect_video_platform(match)
                    })
        
        return videos
    
    def _analyze_audio(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """分析音频元素"""
        audio_elements = []
        
        audio_tags = soup.find_all('audio')
        for audio in audio_tags:
            audio_info = {
                'src': audio.get('src'),
                'controls': audio.has_attr('controls'),
                'autoplay': audio.has_attr('autoplay'),
                'loop': audio.has_attr('loop'),
                'muted': audio.has_attr('muted'),
                'sources': []
            }
            
            sources = audio.find_all('source')
            for source in sources:
                audio_info['sources'].append({
                    'src': source.get('src'),
                    'type': source.get('type')
                })
            
            audio_elements.append(audio_info)
        
        return audio_elements
    
    def _analyze_embeds(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """分析嵌入内容"""
        embeds = []
        
        # 查找embed标签
        embed_tags = soup.find_all('embed')
        for embed in embed_tags:
            embeds.append({
                'type': 'embed',
                'src': embed.get('src'),
                'width': embed.get('width'),
                'height': embed.get('height'),
                'mime_type': embed.get('type')
            })
        
        # 查找object标签
        object_tags = soup.find_all('object')
        for obj in object_tags:
            embeds.append({
                'type': 'object',
                'data': obj.get('data'),
                'width': obj.get('width'),
                'height': obj.get('height'),
                'mime_type': obj.get('type')
            })
        
        return embeds
    
    def _analyze_scripts(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """分析脚本标签"""
        scripts = []
        script_tags = soup.find_all('script')
        
        for script in script_tags:
            script_info = {
                'src': script.get('src'),
                'type': script.get('type'),
                'has_content': bool(script.get_text().strip()),
                'content_length': len(script.get_text()) if script.get_text() else 0
            }
            
            # 检查是否包含视频相关的关键词
            content = script.get_text().lower()
            video_keywords = ['video', 'mp4', 'webm', 'youtube', 'bilibili', 'player']
            script_info['has_video_keywords'] = any(keyword in content for keyword in video_keywords)
            
            scripts.append(script_info)
        
        return scripts
    
    def _extract_meta_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取页面元信息"""
        meta_info = {}
        
        # 提取title
        title_tag = soup.find('title')
        if title_tag:
            meta_info['title'] = title_tag.get_text().strip()
        
        # 提取meta标签
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            name = meta.get('name') or meta.get('property')
            content = meta.get('content')
            if name and content:
                meta_info[name] = content
        
        return meta_info

def main():
    parser = argparse.ArgumentParser(description='分析网页HTML源码中的媒体内容')
    parser.add_argument('url', help='要分析的网页URL')
    parser.add_argument('-o', '--output', help='输出文件路径（JSON格式）')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    analyzer = HTMLAnalyzer()
    
    print(f"正在获取页面: {args.url}")
    html_content = analyzer.fetch_html(args.url)
    
    if not html_content:
        print("无法获取页面内容")
        sys.exit(1)
    
    print("正在分析HTML内容...")
    soup = BeautifulSoup(html_content, 'html.parser')
    analysis_result = analyzer.analyze_media_content(soup)
    
    if args.verbose:
        print(f"发现 {len(analysis_result['videos'])} 个视频")
        print(f"发现 {len(analysis_result['images'])} 个图片")
        print(f"发现 {len(analysis_result['audio'])} 个音频")
        print(f"发现 {len(analysis_result['embeds'])} 个嵌入内容")
    
    # 输出结果
    result_json = json.dumps(analysis_result, ensure_ascii=False, indent=2)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(result_json)
        print(f"分析结果已保存到: {args.output}")
    else:
        print(result_json)

if __name__ == '__main__':
    main()
