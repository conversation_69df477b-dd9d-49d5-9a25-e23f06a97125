#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML分析器的脚本
"""

from html_analyzer import HTMLAnalyzer
import json

def test_sample_urls():
    """测试一些示例URL"""
    analyzer = HTMLAnalyzer()
    
    # 示例URL列表（可以根据实际情况修改）
    test_urls = [
        "https://agree.blinkit.top/archives/12345/",  # 替换为实际的文章URL
        # 可以添加更多测试URL
    ]
    
    for url in test_urls:
        print(f"\n{'='*60}")
        print(f"分析URL: {url}")
        print('='*60)
        
        html_content = analyzer.fetch_html(url)
        if not html_content:
            print("无法获取页面内容")
            continue
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        result = analyzer.analyze_media_content(soup)
        
        print(f"视频数量: {len(result['videos'])}")
        for i, video in enumerate(result['videos']):
            print(f"  视频 {i+1}: {video['type']} - {video.get('src', 'N/A')}")
        
        print(f"图片数量: {len(result['images'])}")
        for i, image in enumerate(result['images'][:3]):  # 只显示前3个
            print(f"  图片 {i+1}: {image.get('src', 'N/A')}")
        
        print(f"音频数量: {len(result['audio'])}")
        print(f"嵌入内容数量: {len(result['embeds'])}")
        
        # 保存详细结果到文件
        output_file = f"analysis_result_{url.split('/')[-2]}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"详细结果已保存到: {output_file}")

def analyze_html_file(file_path):
    """分析本地HTML文件"""
    analyzer = HTMLAnalyzer()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        result = analyzer.analyze_media_content(soup)
        
        print(f"分析文件: {file_path}")
        print(f"视频数量: {len(result['videos'])}")
        print(f"图片数量: {len(result['images'])}")
        print(f"音频数量: {len(result['audio'])}")
        print(f"嵌入内容数量: {len(result['embeds'])}")
        
        return result
    except Exception as e:
        print(f"分析文件失败: {e}")
        return None

def main():
    print("HTML分析器测试")
    print("1. 测试示例URL")
    print("2. 分析本地HTML文件")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == '1':
        test_sample_urls()
    elif choice == '2':
        file_path = input("请输入HTML文件路径: ").strip()
        analyze_html_file(file_path)
    else:
        print("无效选择")

if __name__ == '__main__':
    main()
