import android.graphics.BitmapFactory
import android.text.TextUtils
import android.util.Log
import androidx.compose.ui.graphics.asImageBitmap
import androidx.lifecycle.viewModelScope
import com.chi.gua.app.data.ImageItem
import com.chi.gua.app.utils.ImageDecryptor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jsoup.Jsoup
import java.net.URL

class HomeViewModel : BaseViewModel() {
    private val _images = MutableStateFlow<List<ImageItem>>(emptyList())
    val images: StateFlow<List<ImageItem>> = _images

    private val base_url = "https://51cg1.com"
    private var currentPage = 1
    private var isLastPage = false
    private var isLoadingMore = false

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    init {
        fetchImages()
    }

    private fun fetchImages() {
        if (_isLoading.value) return
        currentPage = 1
        isLastPage = false
        
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val items = fetchPageItems(currentPage)
                _images.value = items
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Failed to fetch images", e)
                _error.value = e.message ?: "Failed to load images"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadMore() {
        if (_isLoading.value || isLoadingMore || isLastPage) return
        
        viewModelScope.launch {
            try {
                isLoadingMore = true
                val nextPage = currentPage + 1
                val newItems = fetchPageItems(nextPage)
                
                if (newItems.isNotEmpty()) {
                    currentPage = nextPage
                    _images.value = _images.value + newItems
                } else {
                    isLastPage = true
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Failed to load more images", e)
                _error.value = e.message ?: "Failed to load more images"
            } finally {
                isLoadingMore = false
            }
        }
    }

    private suspend fun fetchPageItems(page: Int): List<ImageItem> = withContext(Dispatchers.IO) {
        val pageUrl = if (page == 1) base_url else "$base_url/page/$page"
        val htmlContent = fetchWebPageContent(pageUrl)
        val doc = Jsoup.parse(htmlContent.toString())
        val articles = doc.select("article[itemtype='http://schema.org/BlogPosting']")
        
        articles.mapNotNull { article ->
            try {
                // 从script标签中提取图片URL
                val scriptElement = article.select("script").first { 
                    it.data().contains("loadBannerDirect")
                }
                val scriptText = scriptElement.data()
                
                // 提取图片URL - 在loadBannerDirect函数的第一个参数
                val imageUrlRegex = "'(https://pic.*?)'".toRegex()
                val imageUrl = imageUrlRegex.find(scriptText)?.groupValues?.get(1)
                
                // 提取标题和链接
                val titleElement = article.select("h2.post-card-title").first()
                val title = titleElement?.text()?.trim()

                val linkElement = article.select("a[href^='/archives/']").first()
                if (imageUrl != null && titleElement != null && linkElement != null &&
                    !TextUtils.isEmpty(title) && title != "热搜 HOT") {
                    val href = base_url + linkElement.attr("href")
                    
                    Log.d("HomeViewModel", "Found article - Image: $imageUrl, Title: $title, Link: $href")
                    val decryptedBytes = ImageDecryptor.decryptRawBytes(URL(imageUrl).readBytes())
                    decryptedBytes?.let {
                        val bitmap = BitmapFactory.decodeByteArray(decryptedBytes, 0, decryptedBytes.size)
                        val decryptedImage = bitmap.asImageBitmap()
                        ImageItem(
                            bitmap = decryptedImage,
                            title = title,
                            href = href
                        )
                    }
                } else null
                
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Failed to parse article", e)
                null
            }
        }
    }
} 