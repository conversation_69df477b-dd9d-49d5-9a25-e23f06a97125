import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_chi_gua/widgets/fijk_video_player_widget.dart';
import 'package:flutter_chi_gua/widgets/content_block_renderer.dart';
import 'package:flutter_chi_gua/models/content_block.dart';

void main() {
  group('Video Layout Tests', () {
    testWidgets('Video player should respect width constraints', (WidgetTester tester) async {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        platform: 'mp4',
      );

      // Test with different screen widths
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 300, // Constrained width
              child: FijkVideoPlayerWidget(
                videoBlock: videoBlock,
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Find the video player widget
      final videoPlayerFinder = find.byType(FijkVideoPlayerWidget);
      expect(videoPlayerFinder, findsOneWidget);

      // Get the widget and check its constraints
      final videoPlayerWidget = tester.widget<FijkVideoPlayerWidget>(videoPlayerFinder);
      expect(videoPlayerWidget.videoBlock.src, equals('https://example.com/test.mp4'));
    });

    testWidgets('ContentBlockRenderer should handle video blocks with constraints', (WidgetTester tester) async {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        platform: 'mp4',
        title: 'Test Video',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 350),
                child: ContentBlockRenderer(
                  block: videoBlock,
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Verify the ContentBlockRenderer creates the video widget
      expect(find.byType(ContentBlockRenderer), findsOneWidget);
      expect(find.byType(FijkVideoPlayerWidget), findsOneWidget);
    });

    testWidgets('Video player should handle different aspect ratios', (WidgetTester tester) async {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        platform: 'mp4',
        width: 1920,
        height: 1080,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 400,
              height: 600,
              child: FijkVideoPlayerWidget(
                videoBlock: videoBlock,
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Should not overflow and should be properly constrained
      expect(find.byType(FijkVideoPlayerWidget), findsOneWidget);
      expect(find.byType(LayoutBuilder), findsOneWidget);
    });

    testWidgets('Video player should show loading or error state initially', (WidgetTester tester) async {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        platform: 'mp4',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FijkVideoPlayerWidget(
              videoBlock: videoBlock,
            ),
          ),
        ),
      );

      await tester.pump();

      // Should show either loading indicator or error state (since video can't load in tests)
      final hasLoadingIndicator = find.byType(CircularProgressIndicator).evaluate().isNotEmpty;
      final hasErrorState = find.text('加载失败').evaluate().isNotEmpty;

      expect(hasLoadingIndicator || hasErrorState, isTrue);
    });

    test('Video block should handle different video properties', () {
      const videoBlock = VideoBlock(
        src: 'https://example.com/test.mp4',
        poster: 'https://example.com/poster.jpg',
        title: 'Test Video',
        width: 1920,
        height: 1080,
        controls: true,
        autoplay: false,
        platform: 'mp4',
      );

      expect(videoBlock.src, equals('https://example.com/test.mp4'));
      expect(videoBlock.poster, equals('https://example.com/poster.jpg'));
      expect(videoBlock.title, equals('Test Video'));
      expect(videoBlock.width, equals(1920));
      expect(videoBlock.height, equals(1080));
      expect(videoBlock.controls, isTrue);
      expect(videoBlock.autoplay, isFalse);
      expect(videoBlock.platform, equals('mp4'));
    });
  });
}
