import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import 'dart:convert';
import '../models/content_block.dart';
import 'settings_service.dart';

/// HTML内容解析器
/// 将HTML内容解析为ContentBlock列表
class ContentParser {
  final SettingsService _settingsService = SettingsService.instance;

  /// 获取当前的Base URL
  String get baseUrl => _settingsService.baseUrl;

  /// 解析HTML内容为ContentBlock列表
  List<ContentBlock> parseHtml(String html) {
    if (html.isEmpty) return [];

    try {
      final document = html_parser.parse(html);
      final body = document.body;
      
      if (body == null) return [];

      return _parseElement(body);
    } catch (e) {
      print('Error parsing HTML: $e');
      // 如果解析失败，返回一个包含原始HTML的段落块
      return [ParagraphBlock(text: html)];
    }
  }

  /// 解析DOM元素
  List<ContentBlock> _parseElement(Element element) {
    final blocks = <ContentBlock>[];

    for (final node in element.nodes) {
      if (node is Element) {
        blocks.addAll(_parseElementNode(node));
      } else if (node is Text) {
        final text = node.text.trim();
        if (text.isNotEmpty) {
          blocks.add(ParagraphBlock(text: text));
        }
      }
    }

    return blocks;
  }

  /// 解析元素节点
  List<ContentBlock> _parseElementNode(Element element) {
    final tagName = element.localName?.toLowerCase() ?? '';

    switch (tagName) {
      case 'h1':
        return [_parseHeading(element, HeadingLevel.h1)];
      case 'h2':
        return [_parseHeading(element, HeadingLevel.h2)];
      case 'h3':
        return [_parseHeading(element, HeadingLevel.h3)];
      case 'h4':
        return [_parseHeading(element, HeadingLevel.h4)];
      case 'h5':
        return [_parseHeading(element, HeadingLevel.h5)];
      case 'h6':
        return [_parseHeading(element, HeadingLevel.h6)];
      case 'p':
        return _parseParagraph(element);
      case 'img':
        return [_parseImage(element)];
      case 'video':
        return [_parseVideo(element)];
      case 'audio':
        return [_parseAudio(element)];
      case 'iframe':
        return [_parseEmbed(element)];
      case 'a':
        return [_parseLink(element)];
      case 'blockquote':
        return [_parseBlockquote(element)];
      case 'pre':
        return [_parseCodeBlock(element)];
      case 'code':
        return [_parseInlineCode(element)];
      case 'ul':
        return [_parseUnorderedList(element)];
      case 'ol':
        return [_parseOrderedList(element)];
      case 'li':
        return [_parseListItem(element)];
      case 'hr':
        return [const DividerBlock()];
      case 'br':
        return [const LineBreakBlock()];
      case 'strong':
      case 'b':
        return [_parseBold(element)];
      case 'em':
      case 'i':
        return [_parseItalic(element)];
      case 'div':
      case 'span':
      case 'section':
      case 'article':
        // 对于容器元素，递归解析其子元素
        return _parseElement(element);
      default:
        // 对于未知元素，尝试解析其内容
        return _parseUnknownElement(element);
    }
  }

  /// 解析标题
  HeadingBlock _parseHeading(Element element, HeadingLevel level) {
    final text = element.text.trim();
    final styles = _extractStyles(element);
    return HeadingBlock(
      text: text,
      level: level,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析段落
  List<ContentBlock> _parseParagraph(Element element) {
    // 检查段落是否包含混合内容（文字+链接+图片等）
    final hasInlineElements = element.children.any((child) => 
        ['a', 'img', 'strong', 'b', 'em', 'i', 'code'].contains(child.localName));

    if (hasInlineElements) {
      return [_parseMixedContent(element)];
    } else {
      final text = element.text.trim();
      if (text.isNotEmpty) {
        final styles = _extractStyles(element);
        return [ParagraphBlock(
          text: text,
          styles: styles,
          id: element.id.isNotEmpty ? element.id : null,
        )];
      }
    }
    return [];
  }

  /// 解析混合内容
  MixedBlock _parseMixedContent(Element element) {
    final children = <ContentBlock>[];

    for (final node in element.nodes) {
      if (node is Element) {
        // 特殊处理img元素，检查data-xkrkllgl属性
        if (node.localName == 'img') {
          final dataXkrkllgl = node.attributes['data-xkrkllgl'] ?? '';
          final src = node.attributes['src'] ?? '';
          final imageUrl = dataXkrkllgl.isNotEmpty ? dataXkrkllgl : src;

          if (imageUrl.isNotEmpty) {
            final processedSrc = _processImageUrl(imageUrl);
            print('ContentParser: Found inline image - data-xkrkllgl: $dataXkrkllgl, src: $src, final URL: $processedSrc');

            children.add(ImageBlock(
              src: processedSrc,
              alt: node.attributes['alt'],
              title: node.attributes['title'],
              width: _parseDouble(node.attributes['width']),
              height: _parseDouble(node.attributes['height']),
              styles: _extractStyles(node),
              id: node.id.isNotEmpty ? node.id : null,
            ));
          }
        } else {
          children.addAll(_parseElementNode(node));
        }
      } else if (node is Text) {
        final text = node.text.trim();
        if (text.isNotEmpty) {
          children.add(ParagraphBlock(text: text));
        }
      }
    }

    final styles = _extractStyles(element);
    return MixedBlock(
      children: children,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析图片
  ImageBlock _parseImage(Element element) {
    // 优先使用data-xkrkllgl属性（加密图片URL），然后是src属性
    final dataXkrkllgl = element.attributes['data-xkrkllgl'] ?? '';
    final src = element.attributes['src'] ?? '';
    final imageUrl = dataXkrkllgl.isNotEmpty ? dataXkrkllgl : src;

    final alt = element.attributes['alt'];
    final title = element.attributes['title'];
    final width = _parseDouble(element.attributes['width']);
    final height = _parseDouble(element.attributes['height']);
    final styles = _extractStyles(element);

    // 处理相对URL
    final processedSrc = _processImageUrl(imageUrl);

    print('ContentParser: Found image - data-xkrkllgl: $dataXkrkllgl, src: $src, final URL: $processedSrc');

    return ImageBlock(
      src: processedSrc,
      alt: alt,
      title: title,
      width: width,
      height: height,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析视频
  VideoBlock _parseVideo(Element element) {
    final src = element.attributes['src'] ?? '';
    final poster = element.attributes['poster'];
    final width = _parseDouble(element.attributes['width']);
    final height = _parseDouble(element.attributes['height']);
    final controls = element.attributes.containsKey('controls');
    final autoplay = element.attributes.containsKey('autoplay');
    final loop = element.attributes.containsKey('loop');
    final muted = element.attributes.containsKey('muted');
    final styles = _extractStyles(element);

    // 查找source元素
    final sources = <VideoSource>[];
    for (final source in element.querySelectorAll('source')) {
      final sourceSrc = source.attributes['src'];
      final sourceType = source.attributes['type'];
      if (sourceSrc != null && sourceSrc.isNotEmpty) {
        sources.add(VideoSource(
          src: _processImageUrl(sourceSrc),
          type: sourceType,
        ));
      }
    }

    final processedSrc = _processImageUrl(src);
    final platform = _detectVideoPlatform(processedSrc);

    print('ContentParser: Found video - src: $src, platform: $platform, sources: ${sources.length}');

    return VideoBlock(
      src: processedSrc,
      poster: poster != null ? _processImageUrl(poster) : null,
      width: width,
      height: height,
      controls: controls,
      autoplay: autoplay,
      loop: loop,
      muted: muted,
      platform: platform,
      sources: sources.isNotEmpty ? sources : null,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析音频
  AudioBlock _parseAudio(Element element) {
    final src = element.attributes['src'] ?? '';
    final controls = element.attributes.containsKey('controls');
    final autoplay = element.attributes.containsKey('autoplay');
    final loop = element.attributes.containsKey('loop');
    final muted = element.attributes.containsKey('muted');
    final styles = _extractStyles(element);

    // 查找source元素
    final sources = <AudioSource>[];
    for (final source in element.querySelectorAll('source')) {
      final sourceSrc = source.attributes['src'];
      final sourceType = source.attributes['type'];
      if (sourceSrc != null && sourceSrc.isNotEmpty) {
        sources.add(AudioSource(
          src: _processImageUrl(sourceSrc),
          type: sourceType,
        ));
      }
    }

    final processedSrc = _processImageUrl(src);

    print('ContentParser: Found audio - src: $src, sources: ${sources.length}');

    return AudioBlock(
      src: processedSrc,
      controls: controls,
      autoplay: autoplay,
      loop: loop,
      muted: muted,
      sources: sources.isNotEmpty ? sources : null,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析嵌入内容
  EmbedBlock _parseEmbed(Element element) {
    final src = element.attributes['src'] ?? '';
    final width = _parseDouble(element.attributes['width']);
    final height = _parseDouble(element.attributes['height']);
    final title = element.attributes['title'];
    final styles = _extractStyles(element);

    final processedSrc = _processImageUrl(src);
    final platform = _detectVideoPlatform(processedSrc);

    print('ContentParser: Found embed - src: $src, platform: $platform');

    return EmbedBlock(
      src: processedSrc,
      title: title,
      width: width,
      height: height,
      platform: platform,
      embedType: 'iframe',
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析链接
  LinkBlock _parseLink(Element element) {
    final href = element.attributes['href'] ?? '';
    final title = element.attributes['title'];
    final text = element.text.trim();
    final styles = _extractStyles(element);

    return LinkBlock(
      text: text,
      href: href,
      title: title,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析引用块
  BlockquoteBlock _parseBlockquote(Element element) {
    final children = _parseElement(element);
    final styles = _extractStyles(element);

    return BlockquoteBlock(
      children: children,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析代码块
  CodeBlock _parseCodeBlock(Element element) {
    final code = element.text;
    final codeElement = element.querySelector('code');
    final language = codeElement?.attributes['class']?.replaceFirst('language-', '');
    final styles = _extractStyles(element);

    return CodeBlock(
      code: code,
      language: language,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析行内代码
  InlineCodeBlock _parseInlineCode(Element element) {
    final code = element.text;
    final styles = _extractStyles(element);

    return InlineCodeBlock(
      code: code,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析无序列表
  UnorderedListBlock _parseUnorderedList(Element element) {
    final items = <ListItemBlock>[];
    
    for (final li in element.querySelectorAll('li')) {
      final children = _parseElement(li);
      items.add(ListItemBlock(
        children: children,
        id: li.id.isNotEmpty ? li.id : null,
      ));
    }

    final styles = _extractStyles(element);
    return UnorderedListBlock(
      items: items,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析有序列表
  OrderedListBlock _parseOrderedList(Element element) {
    final items = <ListItemBlock>[];
    final start = _parseInt(element.attributes['start']);
    
    for (final li in element.querySelectorAll('li')) {
      final children = _parseElement(li);
      items.add(ListItemBlock(
        children: children,
        id: li.id.isNotEmpty ? li.id : null,
      ));
    }

    final styles = _extractStyles(element);
    return OrderedListBlock(
      items: items,
      start: start,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析列表项
  ListItemBlock _parseListItem(Element element) {
    final children = _parseElement(element);
    final styles = _extractStyles(element);

    return ListItemBlock(
      children: children,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析粗体文本
  BoldBlock _parseBold(Element element) {
    final text = element.text.trim();
    final styles = _extractStyles(element);

    return BoldBlock(
      text: text,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析斜体文本
  ItalicBlock _parseItalic(Element element) {
    final text = element.text.trim();
    final styles = _extractStyles(element);

    return ItalicBlock(
      text: text,
      styles: styles,
      id: element.id.isNotEmpty ? element.id : null,
    );
  }

  /// 解析未知元素
  List<ContentBlock> _parseUnknownElement(Element element) {
    // 对于未知元素，尝试提取其文本内容
    final text = element.text.trim();
    if (text.isNotEmpty) {
      return [ParagraphBlock(text: text)];
    }

    // 如果没有文本内容，递归解析子元素
    return _parseElement(element);
  }

  /// 提取元素样式
  Map<String, String>? _extractStyles(Element element) {
    final style = element.attributes['style'];
    final className = element.attributes['class'];

    if (style == null && className == null) return null;

    final styles = <String, String>{};

    if (style != null) {
      styles['style'] = style;
    }

    if (className != null) {
      styles['class'] = className;
    }

    return styles.isNotEmpty ? styles : null;
  }

  /// 处理图片URL
  String _processImageUrl(String url) {
    if (url.isEmpty) return url;

    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    } else if (url.startsWith('//')) {
      return 'https:$url';
    } else if (url.startsWith('/')) {
      return '$baseUrl$url';
    } else {
      return '$baseUrl/$url';
    }
  }

  /// 检测视频平台
  String? _detectVideoPlatform(String url) {
    if (url.isEmpty) return null;

    final urlLower = url.toLowerCase();

    if (urlLower.contains('youtube.com') || urlLower.contains('youtu.be')) {
      return 'youtube';
    } else if (urlLower.contains('bilibili.com') || urlLower.contains('b23.tv')) {
      return 'bilibili';
    } else if (urlLower.contains('vimeo.com')) {
      return 'vimeo';
    } else if (urlLower.contains('hls.qzkj.tech')) {
      return 'qzkj';
    } else if (urlLower.contains('.m3u8')) {
      return 'hls';
    } else if (urlLower.contains('.mp4')) {
      return 'mp4';
    } else if (urlLower.contains('dailymotion.com')) {
      return 'dailymotion';
    } else if (urlLower.contains('twitch.tv')) {
      return 'twitch';
    } else if (urlLower.contains('tiktok.com')) {
      return 'tiktok';
    }

    return null;
  }

  /// 解析double值
  double? _parseDouble(String? value) {
    if (value == null || value.isEmpty) return null;
    return double.tryParse(value);
  }

  /// 解析int值
  int? _parseInt(String? value) {
    if (value == null || value.isEmpty) return null;
    return int.tryParse(value);
  }

  /// 清理文本内容
  String _cleanText(String text) {
    return text
        .replaceAll(RegExp(r'\s+'), ' ')  // 合并多个空白字符
        .trim();
  }

  /// 检查元素是否为空
  bool _isEmptyElement(Element element) {
    return element.text.trim().isEmpty &&
           element.children.isEmpty;
  }

  /// 合并相邻的文本块
  List<ContentBlock> _mergeAdjacentTextBlocks(List<ContentBlock> blocks) {
    if (blocks.isEmpty) return blocks;

    final merged = <ContentBlock>[];
    ContentBlock? current;

    for (final block in blocks) {
      if (current == null) {
        current = block;
      } else if (current is ParagraphBlock && block is ParagraphBlock) {
        // 合并相邻的段落块
        current = ParagraphBlock(
          text: '${current.text} ${block.text}',
          styles: current.styles ?? block.styles,
          id: current.id ?? block.id,
        );
      } else {
        merged.add(current);
        current = block;
      }
    }

    if (current != null) {
      merged.add(current);
    }

    return merged;
  }

  /// 过滤空白块
  List<ContentBlock> _filterEmptyBlocks(List<ContentBlock> blocks) {
    return blocks.where((block) {
      if (block is ParagraphBlock) {
        return block.text.trim().isNotEmpty;
      } else if (block is HeadingBlock) {
        return block.text.trim().isNotEmpty;
      } else if (block is ImageBlock) {
        return block.src.isNotEmpty;
      } else if (block is LinkBlock) {
        return block.text.trim().isNotEmpty || block.href.isNotEmpty;
      } else if (block is CodeBlock) {
        return block.code.trim().isNotEmpty;
      } else if (block is InlineCodeBlock) {
        return block.code.trim().isNotEmpty;
      } else if (block is BoldBlock) {
        return block.text.trim().isNotEmpty;
      } else if (block is ItalicBlock) {
        return block.text.trim().isNotEmpty;
      }
      return true;
    }).toList();
  }

  /// 后处理内容块列表
  List<ContentBlock> _postProcessBlocks(List<ContentBlock> blocks) {
    // 过滤空白块
    var processed = _filterEmptyBlocks(blocks);

    // 过滤不需要的内容
    processed = _filterUnwantedContent(processed);

    // 合并相邻的文本块
    processed = _mergeAdjacentTextBlocks(processed);

    return processed;
  }

  /// 过滤不需要的内容
  List<ContentBlock> _filterUnwantedContent(List<ContentBlock> blocks) {
    return blocks.where((block) {
      // 获取块的文本内容
      String text = _getBlockText(block);

      // 过滤包含"温馨提示"的内容
      if (text.contains('温馨提示')) {
        return false;
      }

      // 过滤包含"51吃瓜最新下载地址"的内容
      if (text.contains('51吃瓜最新下载地址') ||
          text.contains('51吃瓜') && text.contains('下载地址')) {
        return false;
      }

      // 过滤包含"51同城约炮"的内容
      if (text.contains('51同城约炮')) {
        return false;
      }

      // 过滤包含"51吃瓜国内最新地址"的内容
      if (text.contains('51吃瓜国内最新地址')) {
        return false;
      }

      // 过滤包含推广内容的块
      if (text.contains('最新地址') && text.contains('51')) {
        return false;
      }

      // 过滤包含"同城约炮"的内容
      if (text.contains('同城约炮')) {
        return false;
      }

      // 过滤包含"下载地址"且内容较短的块（可能是广告）
      if (text.contains('下载地址') && text.length < 100) {
        return false;
      }

      // 过滤包含"推广"、"广告"等关键词的短内容
      if ((text.contains('推广') || text.contains('广告') || text.contains('赞助')) && text.length < 50) {
        return false;
      }

      return true;
    }).toList();
  }

  /// 获取内容块的文本内容
  String _getBlockText(ContentBlock block) {
    if (block is ParagraphBlock) {
      return block.text;
    } else if (block is HeadingBlock) {
      return block.text;
    } else if (block is MixedBlock) {
      return block.children.map((child) => _getBlockText(child)).join(' ');
    } else if (block is BlockquoteBlock) {
      return block.children.map((child) => _getBlockText(child)).join(' ');
    } else if (block is ListItemBlock) {
      return block.children.map((child) => _getBlockText(child)).join(' ');
    } else if (block is BoldBlock) {
      return block.text;
    } else if (block is ItalicBlock) {
      return block.text;
    } else if (block is LinkBlock) {
      return block.text;
    }
    return '';
  }

  /// 公共解析方法，包含后处理
  List<ContentBlock> parseHtmlWithPostProcessing(String html) {
    final blocks = parseHtml(html);

    // 提取隐藏的视频URL
    final videoBlocks = _extractVideoFromDataConfig(html);

    // 将视频块添加到开头
    final allBlocks = <ContentBlock>[];
    allBlocks.addAll(videoBlocks);
    allBlocks.addAll(blocks);

    return _postProcessBlocks(allBlocks);
  }

  /// 从data-config属性中提取视频
  List<VideoBlock> _extractVideoFromDataConfig(String html) {
    final videoBlocks = <VideoBlock>[];

    try {
      // 查找data-config属性
      final dataConfigPattern = RegExp(r'data-config\s*=\s*"([^"]+)"');
      final matches = dataConfigPattern.allMatches(html);

      for (final match in matches) {
        final configJson = match.group(1);
        if (configJson != null) {
          try {
            // 解码HTML实体
            final decodedJson = configJson
                .replaceAll('&quot;', '"')
                .replaceAll('&amp;', '&')
                .replaceAll('&lt;', '<')
                .replaceAll('&gt;', '>')
                .replaceAll('&#39;', "'");

            // 解析JSON配置
            final configData = jsonDecode(decodedJson);
            if (configData is Map<String, dynamic> &&
                configData.containsKey('video') &&
                configData['video'] is Map<String, dynamic>) {

              final videoConfig = configData['video'] as Map<String, dynamic>;
              final videoUrl = videoConfig['url'] as String?;

              if (videoUrl != null && videoUrl.isNotEmpty) {
                final cleanUrl = videoUrl.replaceAll(r'\/', '/');
                final platform = _detectVideoPlatform(cleanUrl);
                final videoType = videoConfig['type'] as String?;

                print('ContentParser: Found video from data-config - URL: $cleanUrl, platform: $platform, type: $videoType');

                videoBlocks.add(VideoBlock(
                  src: cleanUrl,
                  platform: platform,
                  controls: true,
                  autoplay: configData['autoplay'] as bool? ?? false,
                  loop: configData['loop'] as bool? ?? false,
                  muted: false,
                ));
              }
            }
          } catch (e) {
            print('ContentParser: Error parsing video config JSON: $e');
            // 如果JSON解析失败，尝试正则提取
            final urlMatch = RegExp(r'"url"\s*:\s*"([^"]+)"').firstMatch(configJson);
            if (urlMatch != null) {
              final videoUrl = urlMatch.group(1)?.replaceAll(r'\/', '/');
              if (videoUrl != null && videoUrl.isNotEmpty) {
                final platform = _detectVideoPlatform(videoUrl);
                print('ContentParser: Found video from regex - URL: $videoUrl, platform: $platform');

                videoBlocks.add(VideoBlock(
                  src: videoUrl,
                  platform: platform,
                  controls: true,
                  autoplay: false,
                  loop: false,
                  muted: false,
                ));
              }
            }
          }
        }
      }
    } catch (e) {
      print('ContentParser: Error extracting video from data-config: $e');
    }

    return videoBlocks;
  }
}
