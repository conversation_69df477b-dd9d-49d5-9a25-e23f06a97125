/// 分类模型
class Category {
  final String id;
  final String name;
  final String url;
  final String? description;
  final String? iconName;
  final bool isAvailable;

  const Category({
    required this.id,
    required this.name,
    required this.url,
    this.description,
    this.iconName,
    this.isAvailable = true,
  });

  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      url: map['url'] ?? '',
      description: map['description'],
      iconName: map['iconName'],
      isAvailable: map['isAvailable'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'description': description,
      'iconName': iconName,
      'isAvailable': isAvailable,
    };
  }

  Category copyWith({
    String? id,
    String? name,
    String? url,
    String? description,
    String? iconName,
    bool? isAvailable,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  @override
  String toString() {
    return 'Category(id: $id, name: $name, url: $url, isAvailable: $isAvailable)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
