#!/usr/bin/env python3
"""
SVG to PNG converter for Flutter app icon
将SVG logo转换为PNG格式，用于Flutter应用图标生成
"""

import os
import sys
from pathlib import Path

def create_png_from_svg():
    """
    使用cairosvg将SVG转换为PNG
    如果没有安装cairosvg，会提供安装指导
    """
    try:
        import cairosvg
    except ImportError:
        print("需要安装cairosvg库来转换SVG到PNG")
        print("请运行: pip install cairosvg")
        return False
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # SVG文件路径
    svg_path = project_root / "assets" / "logo" / "app_logo.svg"
    png_path = project_root / "assets" / "logo" / "app_logo_1024.png"
    
    if not svg_path.exists():
        print(f"SVG文件不存在: {svg_path}")
        return False
    
    try:
        # 转换SVG到PNG (1024x1024)
        cairosvg.svg2png(
            url=str(svg_path),
            write_to=str(png_path),
            output_width=1024,
            output_height=1024
        )
        
        print(f"成功转换SVG到PNG: {png_path}")
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def create_manual_png():
    """
    如果无法使用cairosvg，创建一个简单的PNG占位符
    """
    try:
        from PIL import Image, ImageDraw
    except ImportError:
        print("需要安装Pillow库: pip install Pillow")
        return False
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    png_path = project_root / "assets" / "logo" / "app_logo_1024.png"
    
    # 创建1024x1024的图像
    size = 1024
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # 绘制圆形背景
    margin = 50
    circle_bbox = [margin, margin, size - margin, size - margin]
    draw.ellipse(circle_bbox, fill='#FF5722')
    
    # 绘制瓜子形状（简化的椭圆）
    seed_center_x, seed_center_y = size // 2, size // 2
    seed_width, seed_height = 350, 200
    seed_bbox = [
        seed_center_x - seed_width // 2,
        seed_center_y - seed_height // 2,
        seed_center_x + seed_width // 2,
        seed_center_y + seed_height // 2
    ]
    draw.ellipse(seed_bbox, fill='#5D2F1A')
    
    # 绘制眼睛
    eye_radius = 120
    eye_bbox = [
        seed_center_x - eye_radius,
        seed_center_y - eye_radius,
        seed_center_x + eye_radius,
        seed_center_y + eye_radius
    ]
    draw.ellipse(eye_bbox, fill='white')
    
    # 绘制瞳孔
    pupil_radius = 80
    pupil_bbox = [
        seed_center_x - pupil_radius,
        seed_center_y - pupil_radius,
        seed_center_x + pupil_radius,
        seed_center_y + pupil_radius
    ]
    draw.ellipse(pupil_bbox, fill='#2E2E2E')
    
    # 绘制高光
    highlight_radius = 25
    highlight_x, highlight_y = seed_center_x - 30, seed_center_y - 30
    highlight_bbox = [
        highlight_x - highlight_radius,
        highlight_y - highlight_radius,
        highlight_x + highlight_radius,
        highlight_y + highlight_radius
    ]
    draw.ellipse(highlight_bbox, fill='white')
    
    # 保存图像
    image.save(png_path, 'PNG')
    print(f"成功创建PNG图标: {png_path}")
    return True

if __name__ == "__main__":
    print("开始转换SVG到PNG...")
    
    # 首先尝试使用cairosvg
    if create_png_from_svg():
        print("使用cairosvg转换成功!")
    else:
        print("cairosvg转换失败，尝试手动创建PNG...")
        if create_manual_png():
            print("手动创建PNG成功!")
        else:
            print("PNG创建失败!")
            sys.exit(1)
    
    print("转换完成! 现在可以运行 'flutter packages pub run flutter_launcher_icons:main' 来生成应用图标")
