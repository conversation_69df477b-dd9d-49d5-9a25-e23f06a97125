import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/content_block.dart';
import '../screens/fullscreen_video_player_screen.dart';
import '../services/video_launcher_service.dart';

/// 基于video_player的视频播放器Widget
/// 支持mp4、m3u8等格式的视频播放
class FijkVideoPlayerWidget extends StatefulWidget {
  final VideoBlock videoBlock;
  final VoidCallback? onTap;

  const FijkVideoPlayerWidget({
    super.key,
    required this.videoBlock,
    this.onTap,
  });

  @override
  State<FijkVideoPlayerWidget> createState() => _FijkVideoPlayerWidgetState();
}

class _FijkVideoPlayerWidgetState extends State<FijkVideoPlayerWidget> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isLoading = true;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  /// 初始化播放器
  Future<void> _initializePlayer() async {
    try {
      // 创建视频播放器控制器
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.videoBlock.src),
      );

      // 监听播放器状态
      _controller!.addListener(_onPlayerStateChanged);

      // 初始化播放器
      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error initializing video player: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  /// 播放器状态变化监听
  void _onPlayerStateChanged() {
    if (!mounted || _controller == null) return;

    if (_controller!.value.hasError) {
      setState(() {
        _hasError = true;
        _errorMessage = _controller!.value.errorDescription ?? '视频播放出错';
      });
    }
  }

  /// 使用外部播放器播放视频
  Future<void> _launchExternalPlayer() async {
    try {
      final videoUrl = widget.videoBlock.src;

      // 优先使用平台通道方式，特别是对于 m3u8 格式
      try {
        final success = await VideoLauncherService.launchVideoPlayer(videoUrl);
        if (success) {
          return;
        }
      } on VideoLauncherException catch (e) {
        debugPrint('Platform channel launch failed: ${e.message}');
        // 继续尝试 url_launcher 方式
      } catch (e) {
        debugPrint('Platform channel launch error: $e');
        // 继续尝试 url_launcher 方式
      }

      // 备用方案：使用 url_launcher
      final uri = Uri.parse(videoUrl);
      final canLaunch = await canLaunchUrl(uri);

      if (canLaunch) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('无法打开外部播放器，请确保设备上安装了支持该格式的播放器'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('启动外部播放器失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 获取可用宽度，考虑到父容器的约束
          final availableWidth = constraints.maxWidth;

          // 计算视频的宽高比，如果视频未初始化则使用默认16:9
          double aspectRatio = 16 / 9; // 默认宽高比
          if (_isInitialized && _controller != null && _controller!.value.aspectRatio > 0) {
            aspectRatio = _controller!.value.aspectRatio;
          }

          // 根据可用宽度和宽高比计算高度
          final calculatedHeight = availableWidth / aspectRatio;

          // 设置最大高度限制，防止在极端宽高比下溢出
          final maxHeight = MediaQuery.of(context).size.height * 0.4; // 屏幕高度的40%
          final finalHeight = calculatedHeight > maxHeight ? maxHeight : calculatedHeight;

          return Container(
            width: availableWidth,
            height: finalHeight,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildVideoContent(),
            ),
          );
        },
      ),
    );
  }

  /// 构建视频内容
  Widget _buildVideoContent() {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    if (!_isInitialized || _controller == null) {
      return _buildLoadingWidget();
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
      },
      child: Stack(
        children: [
          // 视频播放器 - 使用 Positioned.fill 确保填满整个容器
          Positioned.fill(
            child: FittedBox(
              fit: BoxFit.contain,
              child: _controller!.value.size.width > 0 && _controller!.value.size.height > 0
                  ? SizedBox(
                      width: _controller!.value.size.width,
                      height: _controller!.value.size.height,
                      child: VideoPlayer(_controller!),
                    )
                  : AspectRatio(
                      aspectRatio: _controller!.value.aspectRatio > 0
                          ? _controller!.value.aspectRatio
                          : 16 / 9,
                      child: VideoPlayer(_controller!),
                    ),
            ),
          ),

          // 控制面板
          if (_showControls) _buildControlPanel(),

          // 平台标识和外部播放器按钮
          if (widget.videoBlock.platform != null || _showControls)
            Positioned(
              top: 12,
              right: 12,
              child: _buildTopRightControls(),
            ),

          // 全屏按钮
          if (_showControls)
            Positioned(
              bottom: 12,
              right: 12,
              child: _buildFullscreenButton(),
            ),
        ],
      ),
    );
  }

  /// 构建加载中Widget
  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min, // 防止溢出
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 8), // 减小间距
            Flexible(
              child: Text(
                '正在加载视频...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误Widget
  Widget _buildErrorWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.white,
                  size: 24, // 减小图标尺寸
                ),
                const SizedBox(height: 4),
                const Text(
                  '加载失败',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _hasError = false;
                          _isLoading = true;
                        });
                        _initializePlayer();
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        minimumSize: const Size(60, 24),
                      ),
                      child: const Text(
                        '重试',
                        style: TextStyle(fontSize: 10),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _launchExternalPlayer,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        minimumSize: const Size(60, 24),
                        backgroundColor: Colors.blue,
                      ),
                      child: const Text(
                        '外部播放器',
                        style: TextStyle(fontSize: 10, color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建右上角控件（平台标识和外部播放器按钮）
  Widget _buildTopRightControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 平台标识
        if (widget.videoBlock.platform != null) _buildPlatformBadge(),

        // 间距
        if (widget.videoBlock.platform != null && _showControls)
          const SizedBox(width: 8),

        // 外部播放器按钮
        if (_showControls)
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(16),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.open_in_new,
                color: Colors.white,
                size: 16,
              ),
              iconSize: 16,
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              tooltip: '使用外部播放器',
              onPressed: _launchExternalPlayer,
            ),
          ),
      ],
    );
  }

  /// 构建平台标识
  Widget _buildPlatformBadge() {
    final platform = widget.videoBlock.platform;
    if (platform == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getPlatformColor(platform).withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        platform.toUpperCase(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 获取平台颜色
  Color _getPlatformColor(String platform) {
    switch (platform.toLowerCase()) {
      case 'youtube':
        return const Color(0xFFFF0000);
      case 'bilibili':
        return const Color(0xFF00A1D6);
      case 'vimeo':
        return const Color(0xFF1AB7EA);
      case 'hls':
        return const Color(0xFF10B981);
      case 'mp4':
        return const Color(0xFF8B5CF6);
      default:
        return const Color(0xFF6B7280);
    }
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    if (_controller == null) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 顶部控制栏
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.videoBlock.title ?? '视频播放',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // 中央播放按钮
          Center(
            child: IconButton(
              icon: Icon(
                _controller!.value.isPlaying
                    ? Icons.pause_circle_filled
                    : Icons.play_circle_filled,
                color: Colors.white,
                size: 64,
              ),
              onPressed: () {
                setState(() {
                  if (_controller!.value.isPlaying) {
                    _controller!.pause();
                  } else {
                    _controller!.play();
                  }
                });
              },
            ),
          ),

          // 底部进度条
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 进度条
                VideoProgressIndicator(
                  _controller!,
                  allowScrubbing: true,
                  colors: const VideoProgressColors(
                    playedColor: Colors.red,
                    bufferedColor: Colors.grey,
                    backgroundColor: Colors.black26,
                  ),
                ),
                const SizedBox(height: 8),
                // 时间显示
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(_controller!.value.position),
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                    Text(
                      _formatDuration(_controller!.value.duration),
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建全屏按钮
  Widget _buildFullscreenButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(20),
      ),
      child: IconButton(
        icon: const Icon(
          Icons.fullscreen,
          color: Colors.white,
          size: 24,
        ),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FullscreenVideoPlayerScreen(
                videoBlock: widget.videoBlock,
              ),
            ),
          );
        },
      ),
    );
  }

  /// 格式化时间显示
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }
}
