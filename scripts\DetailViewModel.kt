package com.chi.gua.app.viewmodel

import android.graphics.BitmapFactory
import android.util.Log
import androidx.compose.ui.graphics.asImageBitmap
import androidx.lifecycle.viewModelScope
import com.chi.gua.app.data.ContentItem
import com.chi.gua.app.utils.ImageDecryptor
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jsoup.Jsoup
import org.jsoup.nodes.Element
import org.jsoup.nodes.TextNode
import java.net.URL

private const val TAG = "DetailViewModel"

class DetailViewModel : BaseViewModel() {
    private val _contents = MutableStateFlow<List<ContentItem>>(emptyList())
    val contents: StateFlow<List<ContentItem>> = _contents

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    private val gson = Gson()

    fun fetchDetail(url: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                _contents.value = withContext(Dispatchers.IO) {
                    parseContent(url)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to fetch detail", e)
                _error.value = e.message ?: "Failed to load detail"
            } finally {
                _isLoading.value = false
            }
        }
    }

    private suspend fun parseContent(url: String): List<ContentItem> {
        val htmlContent = fetchWebPageContent(url)
        val doc = Jsoup.parse(htmlContent.toString())
        val mainContent = doc.selectFirst("div.post-content") ?: throw IllegalStateException("Content not found")
        
        // 移除不需要的部分
        mainContent.select("div.txt-apps, blockquote, div.content-tabs, div.line, div.tags").remove()
        // 移除包含关键词的<p>标签
        mainContent.select("p:contains(关键词)").remove()

        return mainContent.children().mapNotNull { element ->
            when (element.tagName()) {
                "p" -> parseParagraphElement(element)
                "img" -> parseImageElement(element)
                "div" -> parseVideoElement(element)
                else -> null
            }
        }.flatten()
    }

    private suspend fun parseParagraphElement(element: Element): List<ContentItem> {
        val items = mutableListOf<ContentItem>()
        element.childNodes().forEachIndexed { index, childNode ->
            if (childNode is TextNode) {
                // 处理文本内容
                val text = childNode.toString()
                if (text.isNotEmpty()) {
                    items.add(ContentItem.TextContent(text))
                }
            }else if (childNode is Element){
                if (childNode.nodeName() == "img") {
                    val imageUrl = childNode.attr("data-xkrkllgl")
                    parseImageUrl(imageUrl)?.let {
                        items.add(it)
                    }
                }
            }
        }
        return items
    }

    private suspend fun parseImageElement(element: Element): List<ContentItem> {
        val imageUrl = element.attr("data-xkrkllgl")
        return parseImageUrl(imageUrl)?.let { listOf(it) } ?: emptyList()
    }

    private suspend fun parseImageUrl(url: String): ContentItem.ImageContent? {
        if (url.isEmpty()) return null
        
        return try {
            withContext(Dispatchers.IO) {
                val decryptedBytes = ImageDecryptor.decryptRawBytes(URL(url).readBytes())
                val bitmap = BitmapFactory.decodeByteArray(decryptedBytes, 0, decryptedBytes?.size!!)
                ContentItem.ImageContent(
                    bitmap = bitmap?.asImageBitmap(),
                    originalUrl = url
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load image: $url", e)
            null
        }
    }

    private suspend fun parseVideoElement(element: Element): List<ContentItem> {
        val dataConfig = element.attr("data-config")
        if (dataConfig.isEmpty()) return emptyList()

        return try {
            val json = gson.fromJson(dataConfig, JsonObject::class.java)
            val videoObj = json.getAsJsonObject("video")
            val videoUrl = videoObj.get("url").asString
            val thumbnailUrl = videoObj.get("pic").asString

            val thumbnailBitmap = withContext(Dispatchers.IO) {
                try {
                    val decryptedBytes = ImageDecryptor.decryptRawBytes(URL(thumbnailUrl).readBytes())
                    BitmapFactory.decodeByteArray(decryptedBytes, 0, decryptedBytes?.size!!)?.asImageBitmap()
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to load video thumbnail: $thumbnailUrl", e)
                    null
                }
            }

            listOf(ContentItem.VideoContent(
                thumbnailBitmap = thumbnailBitmap,
                videoUrl = videoUrl
            ))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse video config", e)
            emptyList()
        }
    }
} 