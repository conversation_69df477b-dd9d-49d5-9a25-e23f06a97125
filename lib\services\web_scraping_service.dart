import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import '../models/article.dart';
import 'content_parser.dart';
import 'settings_service.dart';
import '../utils/http_headers_util.dart';

class WebScrapingService {
  final SettingsService _settingsService = SettingsService.instance;
  final ContentParser _contentParser = ContentParser();

  /// 获取当前的Base URL
  String get baseUrl => _settingsService.baseUrl;

  /// 获取分类URL
  String get categoryUrl => _settingsService.getCategoryUrl();

  // 获取文章列表
  Future<List<Article>> fetchArticles({int page = 1, String? categoryUrl, int sourceIndex = 0}) async {
    try {
      // 使用传入的分类URL，如果没有则使用默认的
      final baseUrl = categoryUrl ?? this.categoryUrl;
      // 正确的分页URL格式：/category/wpcz/ 或 /category/wpcz/2/
      final url = page == 1 ? baseUrl : '$baseUrl$page/';
      print('WebScrapingService: Fetching articles from URL: $url (page: $page)');

      final response = await http.get(
        Uri.parse(url),
        headers: HttpHeadersUtil.getBasicHeaders(),
      );

      if (response.statusCode == 200) {
        final articles = await _parseArticleList(response.body, sourceIndex);
        print('WebScrapingService: Parsed ${articles.length} articles from page $page');
        return articles;
      } else {
        print('WebScrapingService: HTTP error ${response.statusCode} for page $page');
        throw Exception('Failed to load articles: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching articles: $e');
    }
  }

  // 获取文章详细内容
  Future<Article> fetchArticleContent(Article article) async {
    try {
      final response = await http.get(
        Uri.parse(article.url),
        headers: HttpHeadersUtil.getBasicHeaders(),
      );

      if (response.statusCode == 200) {
        final content = _parseArticleContent(response.body);
        final contentBlocks = _contentParser.parseHtmlWithPostProcessing(content);

        // 尝试从文章详情页面重新提取更准确的发布日期
        final publishDate = _extractPublishDateFromArticlePage(response.body) ?? article.publishDate;

        return article.copyWith(
          content: content,
          contentBlocks: contentBlocks,
          publishDate: publishDate,
        );
      } else {
        throw Exception('Failed to load article content: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching article content: $e');
    }
  }

  // 解析文章列表
  Future<List<Article>> _parseArticleList(String html, [int sourceIndex = 0]) async {
    final document = html_parser.parse(html);
    final articles = <Article>[];

    // 使用正确的选择器查找文章元素
    final articleElements = document.querySelectorAll('article[itemtype="http://schema.org/BlogPosting"]');

    for (final article in articleElements) {
      try {
        // 提取标题
        final titleElement = article.querySelector('h2.post-card-title');
        if (titleElement == null) continue;

        final title = titleElement.text.trim();
        if (title.isEmpty || title == "热搜 HOT") continue;

        // 提取链接
        final linkElement = article.querySelector('a[href^="/archives/"]');
        if (linkElement == null) continue;

        final href = linkElement.attributes['href'];
        if (href == null || href.isEmpty) continue;

        // 构建完整URL
        final fullUrl = _settingsService.getFullUrl(href);

        // 提取文章ID
        final idMatch = RegExp(r'/archives/(\d+)/').firstMatch(href);
        final id = idMatch?.group(1) ?? '';

        if (id.isEmpty) continue;

        // 从script标签中提取图片URL（参考Android代码）
        String? imageUrl;
        final scriptElements = article.querySelectorAll('script');
        for (final script in scriptElements) {
          final scriptText = script.text;
          if (scriptText.contains('loadBannerDirect')) {
            // 使用正则表达式提取图片URL
            final imageUrlRegex = RegExp(r"'(https://pic.*?)'");
            final match = imageUrlRegex.firstMatch(scriptText);
            if (match != null) {
              final extractedUrl = match.group(1);
              if (extractedUrl != null) {
                // 验证图片URL是否可访问
                imageUrl = await _validateImageUrl(extractedUrl);
              }
              break;
            }
          }
        }

        // 查找作者和日期信息
        String author = '瓜友';
        DateTime publishDate = DateTime.now();

        // 从链接元素中提取作者和日期信息
        // 链接文本格式类似: "标题 作者 • YYYY 年 MM 月 DD 日 • 分类"
        final linkText = linkElement.text.trim();
        final parts = linkText.split('•');

        if (parts.length >= 2) {
          // 提取作者信息（第一个 • 之前的最后一部分）
          final beforeFirstDot = parts[0].trim();
          final titleAndAuthor = beforeFirstDot.split(' ');
          if (titleAndAuthor.length > 1) {
            author = titleAndAuthor.last.trim();
          }

          // 提取日期信息（第一个 • 之后的部分）
          final dateText = parts[1].trim();
          final parsedDate = _parseDate(dateText);
          if (parsedDate != null) {
            publishDate = parsedDate;
          }
        }

        // 创建文章对象
        final articleItem = Article(
          id: id,
          title: title,
          url: fullUrl,
          excerpt: title, // 暂时使用标题作为摘要
          author: author,
          publishDate: publishDate,
          categories: ['今日吃瓜'],
          imageUrl: imageUrl,
          sourceIndex: sourceIndex,
          sourceName: _settingsService.getSourceName(sourceIndex),
        );

        articles.add(articleItem);
      } catch (e) {
        // 跳过解析失败的文章
        continue;
      }
    }

    return articles;
  }

  // 解析文章内容
  String _parseArticleContent(String html) {
    final document = html_parser.parse(html);
    
    // 尝试多种选择器来找到文章内容
    final contentSelectors = [
      '.post-content',
      '.entry-content',
      '.article-content',
      '.content',
      'article',
      '.post',
      '.entry',
    ];

    Element? contentElement;
    for (final selector in contentSelectors) {
      contentElement = document.querySelector(selector);
      if (contentElement != null) break;
    }

    if (contentElement != null) {
      // 移除不需要的元素
      contentElement.querySelectorAll('script, style, nav, .navigation, .sidebar, .footer, .header, .ads, .advertisement').forEach((el) => el.remove());
      
      return contentElement.innerHtml;
    }

    // 如果没有找到内容区域，返回body内容
    final bodyElement = document.querySelector('body');
    if (bodyElement != null) {
      // 移除不需要的元素
      bodyElement.querySelectorAll('script, style, nav, .navigation, .sidebar, .footer, .header, .ads, .advertisement').forEach((el) => el.remove());
      return bodyElement.innerHtml;
    }

    return html;
  }

  // 验证图片URL是否可访问
  Future<String?> _validateImageUrl(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      if (response.statusCode == 200) {
        return url;
      }
    } catch (e) {
      // 图片URL验证失败，静默处理
    }
    return null;
  }



  // 从文章详情页面提取发布日期
  DateTime? _extractPublishDateFromArticlePage(String html) {
    try {
      final document = html_parser.parse(html);

      // 尝试多种选择器来找到发布日期
      final dateSelectors = [
        '.post-date',
        '.entry-date',
        '.publish-date',
        '.article-date',
        'time[datetime]',
        '.date',
        '[itemprop="datePublished"]',
        '.post-meta .date',
        '.entry-meta .date',
      ];

      for (final selector in dateSelectors) {
        final dateElement = document.querySelector(selector);
        if (dateElement != null) {
          // 尝试从datetime属性获取
          final datetime = dateElement.attributes['datetime'];
          if (datetime != null) {
            final parsedDate = DateTime.tryParse(datetime);
            if (parsedDate != null) return parsedDate;
          }

          // 尝试从文本内容获取
          final dateText = dateElement.text.trim();
          if (dateText.isNotEmpty) {
            final parsedDate = _parseDate(dateText);
            if (parsedDate != null) return parsedDate;
          }
        }
      }

      // 如果没有找到专门的日期元素，尝试从页面内容中搜索日期模式
      final pageText = document.body?.text ?? '';
      return _parseDate(pageText);
    } catch (e) {
      return null;
    }
  }

  // 解析日期字符串
  DateTime? _parseDate(String dateText) {
    try {
      // 清理日期文本，移除多余的空格和符号
      final cleanedText = dateText.trim().replaceAll(RegExp(r'\s+'), ' ');

      // 尝试解析常见的日期格式
      final patterns = [
        // 中文格式: 2025 年 07 月 08 日
        RegExp(r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日'),
        // 标准格式: 2025-07-08
        RegExp(r'(\d{4})-(\d{1,2})-(\d{1,2})'),
        // 斜杠格式: 2025/07/08
        RegExp(r'(\d{4})/(\d{1,2})/(\d{1,2})'),
        // 点分格式: 2025.07.08
        RegExp(r'(\d{4})\.(\d{1,2})\.(\d{1,2})'),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(cleanedText);
        if (match != null) {
          final year = int.parse(match.group(1)!);
          final month = int.parse(match.group(2)!);
          final day = int.parse(match.group(3)!);
          return DateTime(year, month, day);
        }
      }

      // 尝试直接解析ISO格式
      return DateTime.tryParse(cleanedText);
    } catch (e) {
      return null;
    }
  }
}
