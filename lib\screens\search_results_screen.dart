import 'package:flutter/material.dart';
import '../models/search_result.dart';
import '../models/article.dart';
import '../services/search_service.dart';
import '../services/settings_service.dart';
import '../widgets/encrypted_network_image.dart';
import 'article_detail_screen.dart';

/// 搜索结果页面
class SearchResultsScreen extends StatefulWidget {
  final String query;
  final String sortType;

  const SearchResultsScreen({
    super.key,
    required this.query,
    this.sortType = 'relevance',
  });

  @override
  State<SearchResultsScreen> createState() => _SearchResultsScreenState();
}

class _SearchResultsScreenState extends State<SearchResultsScreen> {
  final SearchService _searchService = SearchService();
  final SettingsService _settingsService = SettingsService.instance;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  SearchResult? _searchResult;
  SearchResult? _filteredResult; // 当前显示的过滤后结果
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasError = false;
  String _errorMessage = '';
  String _currentSortType = 'relevance';
  int? _selectedSourceIndex; // 当前选中的数据源索引，null表示显示所有源

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.query;
    _currentSortType = widget.sortType;
    // 默认选择第一个数据源（索引0）
    _selectedSourceIndex = 0;
    _performSearch();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// 执行搜索
  Future<void> _performSearch({bool isLoadMore = false}) async {
    if (!mounted) return;

    final query = _searchController.text.trim();
    if (query.isEmpty) return;

    setState(() {
      if (isLoadMore) {
        _isLoadingMore = true;
      } else {
        _isLoading = true;
        _hasError = false;
        _errorMessage = '';
      }
    });

    try {
      SearchResult result;

      if (isLoadMore && _selectedSourceIndex != null) {
        // 加载更多时，如果选择了特定数据源，只从该数据源加载
        final currentResult = _filteredResult ?? _searchResult;
        final page = (currentResult?.currentPage ?? 0) + 1;

        print('🔍 Loading more from single source: ${_selectedSourceIndex}, page: $page');
        print('📊 Current result before load: ${currentResult?.toString()}');

        result = await _searchService.searchArticlesFromSource(
          query: query,
          page: page,
          sortType: _currentSortType,
          sourceIndex: _selectedSourceIndex!,
        );

        print('📊 New result from single source: ${result.toString()}');
      } else {
        // 新搜索或加载更多（显示所有数据源）
        final page = isLoadMore ? (_searchResult?.currentPage ?? 0) + 1 : 1;

        print('🔍 ${isLoadMore ? "Loading more" : "New search"} from all sources, page: $page');
        if (isLoadMore) {
          print('📊 Current result before load: ${_searchResult?.toString()}');
        }

        result = await _searchService.searchArticles(
          query: query,
          page: page,
          sortType: _currentSortType,
        );

        print('📊 New result from all sources: ${result.toString()}');
      }

      if (!mounted) return;

      setState(() {
        if (isLoadMore) {
          if (_selectedSourceIndex != null && _filteredResult != null) {
            // 合并到过滤后的结果
            print('🔄 Merging to filtered result');
            print('📊 Before merge - Filtered: ${_filteredResult!.toString()}');
            _filteredResult = _filteredResult!.merge(result);
            print('📊 After merge - Filtered: ${_filteredResult!.toString()}');

            // 同时更新原始结果（添加新的文章）
            if (_searchResult != null) {
              print('📊 Before update - Original: ${_searchResult!.toString()}');
              _searchResult = _searchResult!.copyWith(
                articles: [..._searchResult!.articles, ...result.articles],
                currentPage: result.currentPage,
              );
              print('📊 After update - Original: ${_searchResult!.toString()}');
            }
          } else {
            // 合并到原始结果
            print('🔄 Merging to original result');
            print('📊 Before merge - Original: ${_searchResult!.toString()}');
            _searchResult = _searchResult!.merge(result);
            print('📊 After merge - Original: ${_searchResult!.toString()}');
            _updateFilteredResult();
          }
        } else {
          _searchResult = result;
          _updateFilteredResult();
        }
        _isLoading = false;
        _isLoadingMore = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  /// 滚动监听
  void _onScroll() {
    // 检查是否滚动到接近底部（距离底部200像素）
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {

      // 获取当前显示的结果（过滤后的结果或原始结果）
      final currentResult = _filteredResult ?? _searchResult;

      // 调试日志
      print('🔄 Scroll detected - Page: ${currentResult?.currentPage}/${currentResult?.totalPages}, HasMore: ${currentResult?.hasMorePages}');

      // 检查是否可以加载更多
      if (!_isLoadingMore &&
          !_isLoading &&
          currentResult != null &&
          currentResult.hasMorePages) {

        print('✅ Loading page ${currentResult.currentPage + 1}');
        _performSearch(isLoadMore: true);
      } else {
        print('❌ Load blocked - HasMorePages: ${currentResult?.hasMorePages}');
      }
    }
  }

  /// 新搜索
  void _onNewSearch() {
    _performSearch();
  }

  /// 改变排序方式
  void _onSortChanged(String sortType) {
    if (_currentSortType != sortType) {
      setState(() {
        _currentSortType = sortType;
      });
      _performSearch();
    }
  }

  /// 更新过滤后的结果
  void _updateFilteredResult() {
    print('🔄 UpdateFilteredResult called');
    print('📊 Search result: ${_searchResult?.toString()}');
    print('📍 Selected source index: $_selectedSourceIndex');

    if (_searchResult == null) {
      _filteredResult = null;
      print('📊 No search result, filtered result set to null');
      return;
    }

    if (_selectedSourceIndex == null) {
      // 显示所有数据源的结果
      _filteredResult = _searchResult;
      print('📊 Showing all sources, filtered result = search result');
    } else {
      // 显示指定数据源的结果
      _filteredResult = _searchService.filterResultsBySource(_searchResult!, _selectedSourceIndex!);
      print('📊 Filtered result updated for source $_selectedSourceIndex');
    }

    print('📊 Final filtered result: ${_filteredResult?.toString()}');
  }

  /// 选择数据源
  void _onSourceSelected(int? sourceIndex) {
    if (_selectedSourceIndex != sourceIndex) {
      setState(() {
        _selectedSourceIndex = sourceIndex;
        _updateFilteredResult();
      });
    }
  }

  /// 点击文章
  void _onArticleTap(Article article) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArticleDetailScreen(article: article),
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('搜索结果'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          if (_searchResult != null) _buildSortBar(),
          Expanded(child: _buildMainContent()),
        ],
      ),
    );
  }

  /// 构建主要内容区域（左右分栏布局）
  Widget _buildMainContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在搜索...'),
          ],
        ),
      );
    }

    if (_hasError) {
      return _buildErrorContent();
    }

    // 强制使用左右分栏布局
    return _buildWideScreenLayout();
  }

  /// 构建宽屏布局（左右分栏）
  Widget _buildWideScreenLayout() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据屏幕宽度动态调整左侧面板宽度，保持较窄的比例
        final panelWidth = constraints.maxWidth > 800 ? 200.0 :
                          constraints.maxWidth > 600 ? 160.0 : 120.0;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 左侧数据源面板
            Container(
              width: panelWidth,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: _buildSourcePanel(),
            ),
            // 右侧结果面板
            Expanded(
              child: _buildResultsPanel(),
            ),
          ],
        );
      },
    );
  }

  /// 构建窄屏布局（垂直布局）
  Widget _buildNarrowScreenLayout() {
    return Column(
      children: [
        // 数据源选择器（水平滚动）
        _buildSourceSelector(),
        // 结果面板
        Expanded(
          child: _buildResultsPanel(),
        ),
      ],
    );
  }

  /// 构建错误内容
  Widget _buildErrorContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '搜索失败',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _performSearch,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索全站内容...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            icon: const Icon(Icons.search),
            onPressed: _onNewSearch,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
        ),
        onSubmitted: (_) => _onNewSearch(),
      ),
    );
  }

  /// 构建数据源面板（左侧面板）
  Widget _buildSourcePanel() {
    // 获取所有可用的数据源
    final allBaseUrls = _settingsService.allBaseUrls;
    final availableSources = _searchResult?.availableSources ?? List.generate(allBaseUrls.length, (index) => index);
    final articlesBySource = _searchResult?.articlesBySource ?? <int, List<Article>>{};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 数据源列表（移除标题栏）
        Expanded(
          child: ListView(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
            children: [
              // "全部"选项
              _buildSourceItem(
                sourceIndex: null,
                sourceName: '全部',
                count: _searchResult?.articles.length ?? 0,
                isSelected: _selectedSourceIndex == null,
              ),
              const SizedBox(height: 4),
              // 各个数据源
              ...availableSources.map((sourceIndex) {
                final articles = articlesBySource[sourceIndex] ?? [];
                final sourceName = _settingsService.getSourceName(sourceIndex);
                return _buildSourceItem(
                  sourceIndex: sourceIndex,
                  sourceName: sourceName,
                  count: articles.length,
                  isSelected: _selectedSourceIndex == sourceIndex,
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建数据源选择器（窄屏顶部）
  Widget _buildSourceSelector() {
    if (_searchResult == null) return const SizedBox.shrink();

    final availableSources = _searchResult!.availableSources;
    final articlesBySource = _searchResult!.articlesBySource;

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // "全部"选项
            _buildSourceChip(
              sourceIndex: null,
              sourceName: '全部',
              count: _searchResult!.articles.length,
              isSelected: _selectedSourceIndex == null,
            ),
            const SizedBox(width: 8),
            // 各个数据源
            ...availableSources.map((sourceIndex) {
              final articles = articlesBySource[sourceIndex] ?? [];
              final sourceName = _settingsService.getSourceName(sourceIndex);
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: _buildSourceChip(
                  sourceIndex: sourceIndex,
                  sourceName: sourceName,
                  count: articles.length,
                  isSelected: _selectedSourceIndex == sourceIndex,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建排序栏
  Widget _buildSortBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          const Text('排序: ', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: SearchSortType.values.map((sortType) {
                  final isSelected = _currentSortType == sortType.value;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(sortType.label),
                      selected: isSelected,
                      onSelected: (_) => _onSortChanged(sortType.value),
                      selectedColor: Theme.of(context).colorScheme.primaryContainer,
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建数据源项目（左侧面板中的项目）
  Widget _buildSourceItem({
    required int? sourceIndex,
    required String sourceName,
    required int count,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onSourceSelected(sourceIndex),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: isSelected
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                        : Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    sourceIndex == null ? Icons.apps : Icons.language,
                    size: 14,
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    sourceName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimaryContainer
                          : Theme.of(context).colorScheme.onSurface,
                      fontSize: 13,
                    ),
                  ),
                ),
                if (count > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '$count',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? Theme.of(context).colorScheme.onPrimary
                            : Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建数据源芯片（窄屏顶部的芯片）
  Widget _buildSourceChip({
    required int? sourceIndex,
    required String sourceName,
    required int count,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onSourceSelected(sourceIndex),
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(20),
              border: isSelected
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  sourceIndex == null ? Icons.apps : Icons.language,
                  size: 14,
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 6),
                Text(
                  sourceName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                if (count > 0) ...[
                  const SizedBox(width: 6),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '$count',
                      style: TextStyle(
                        fontSize: 9,
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? Theme.of(context).colorScheme.onPrimary
                            : Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建结果面板
  Widget _buildResultsPanel() {
    final result = _filteredResult ?? _searchResult;

    if (result == null || !result.hasResults) {
      return _buildNoResultsInPanel();
    }

    return _buildResultsList(result);
  }

  /// 构建面板中的无结果提示
  Widget _buildNoResultsInPanel() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _selectedSourceIndex == null ? '未找到相关内容' : '该数据源暂无相关内容',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '尝试选择其他数据源或使用不同的关键词',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在搜索...'),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '搜索失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _performSearch,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_searchResult == null || !_searchResult!.hasResults) {
      return _buildNoResults();
    }

    return _buildResultsList();
  }

  /// 构建无结果页面
  Widget _buildNoResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            '未找到相关内容',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            '尝试使用其他关键词搜索',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          if (_searchResult?.hasSuggestions == true) ...[
            const SizedBox(height: 16),
            const Text('相关搜索建议:'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _searchResult!.suggestions.map((suggestion) {
                return ActionChip(
                  label: Text(suggestion),
                  onPressed: () {
                    _searchController.text = suggestion;
                    _onNewSearch();
                  },
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建结果列表
  Widget _buildResultsList([SearchResult? result]) {
    final searchResult = result ?? _filteredResult ?? _searchResult!;

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: searchResult.articles.length +
                 (_isLoadingMore ? 1 : 0) +
                 (searchResult.hasSuggestions ? 1 : 0),
      itemBuilder: (context, index) {
        // 搜索建议
        if (index == 0 && searchResult.hasSuggestions) {
          return _buildSuggestions(searchResult);
        }

        // 调整索引
        final adjustedIndex = searchResult.hasSuggestions ? index - 1 : index;

        // 加载更多指示器
        if (adjustedIndex >= searchResult.articles.length) {
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final article = searchResult.articles[adjustedIndex];
        return _buildArticleCard(article);
      },
    );
  }

  /// 构建搜索建议
  Widget _buildSuggestions([SearchResult? result]) {
    final searchResult = result ?? _searchResult!;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '相关搜索',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: searchResult.suggestions.map((suggestion) {
                return ActionChip(
                  label: Text(suggestion),
                  onPressed: () {
                    _searchController.text = suggestion;
                    _onNewSearch();
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建文章卡片（与首页样式一致）
  Widget _buildArticleCard(Article article) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _onArticleTap(article),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 封面图片
            if (article.imageUrl != null && article.imageUrl!.isNotEmpty)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: EncryptedNetworkImage(
                    imageUrl: article.imageUrl!,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    placeholder: Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.grey[200],
                      child: const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                    errorWidget: Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.grey[300],
                      child: Icon(
                        Icons.image_not_supported,
                        color: Colors.grey[600],
                        size: 48,
                      ),
                    ),
                  ),
                ),
              ),

            // 文章内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    article.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      // 数据源标识
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: article.sourceIndex == 0
                              ? Colors.blue.withValues(alpha: 0.1)
                              : Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: article.sourceIndex == 0
                                ? Colors.blue.withValues(alpha: 0.3)
                                : Colors.green.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          article.sourceName,
                          style: TextStyle(
                            color: article.sourceIndex == 0
                                ? Colors.blue[700]
                                : Colors.green[700],
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),

                      // 分隔符
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          '•',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ),

                      // 作者信息
                      if (article.author.isNotEmpty) ...[
                        Icon(
                          Icons.person,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          article.author,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),

                        // 分隔符
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          child: Text(
                            '•',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                      ],

                      // 日期信息
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(article.publishDate),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),

                      // 分类标签
                      if (article.categories.isNotEmpty) ...[
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          child: Text(
                            '•',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                        ...article.categories.map((category) {
                          return Container(
                            margin: const EdgeInsets.only(right: 4),
                            child: Text(
                              category,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                                fontSize: 10, // 比日期文字更小
                              ),
                            ),
                          );
                        }),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
