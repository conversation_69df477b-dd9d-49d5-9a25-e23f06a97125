import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_chi_gua/utils/http_headers_util.dart';
import 'package:flutter_chi_gua/services/settings_service.dart';

void main() {
  group('HTTP Headers Util Tests', () {
    test('should return standard headers', () {
      final headers = HttpHeadersUtil.getStandardHeaders();
      
      expect(headers, isNotEmpty);
      expect(headers['User-Agent'], isNotNull);
      expect(headers['Accept'], isNotNull);
      expect(headers['Accept-Language'], isNotNull);
      expect(headers['Accept-Encoding'], isNotNull);
      expect(headers['Connection'], isNotNull);
      expect(headers['Upgrade-Insecure-Requests'], isNotNull);
    });

    test('should return basic headers', () {
      final headers = HttpHeadersUtil.getBasicHeaders();
      
      expect(headers, isNotEmpty);
      expect(headers['User-Agent'], isNotNull);
      expect(headers['Accept'], isNotNull);
      expect(headers['Accept-Language'], isNotNull);
      expect(headers.containsKey('Accept-Encoding'), isFalse);
    });

    test('should return user agent header only', () {
      final headers = HttpHeadersUtil.getUserAgentHeader();
      
      expect(headers.length, equals(1));
      expect(headers['User-Agent'], isNotNull);
    });

    test('should return custom headers', () {
      final customUserAgent = 'Custom User Agent';
      final headers = HttpHeadersUtil.getCustomHeaders(
        userAgent: customUserAgent,
        additionalHeaders: {'Custom-Header': 'Custom-Value'},
      );
      
      expect(headers['User-Agent'], equals(customUserAgent));
      expect(headers['Custom-Header'], equals('Custom-Value'));
    });

    test('should have consistent user agent across methods', () {
      final standardHeaders = HttpHeadersUtil.getStandardHeaders();
      final basicHeaders = HttpHeadersUtil.getBasicHeaders();
      final userAgentHeader = HttpHeadersUtil.getUserAgentHeader();
      
      expect(standardHeaders['User-Agent'], equals(basicHeaders['User-Agent']));
      expect(basicHeaders['User-Agent'], equals(userAgentHeader['User-Agent']));
      expect(userAgentHeader['User-Agent'], equals(HttpHeadersUtil.userAgent));
    });
  });

  group('Settings Service URL Handling Tests', () {
    late SettingsService settingsService;

    setUp(() {
      settingsService = SettingsService.instance;
    });

    test('should convert relative URL to absolute URL', () {
      const relativeUrl = '/archives/123';
      final absoluteUrl = settingsService.getFullUrl(relativeUrl);
      
      expect(absoluteUrl, startsWith('https://'));
      expect(absoluteUrl, endsWith(relativeUrl));
    });

    test('should not modify absolute URLs', () {
      const absoluteUrl = 'https://example.com/test';
      final result = settingsService.getFullUrl(absoluteUrl);
      
      expect(result, equals(absoluteUrl));
    });

    test('should handle URLs without leading slash', () {
      const pathUrl = 'archives/123';
      final absoluteUrl = settingsService.getFullUrl(pathUrl);
      
      expect(absoluteUrl, startsWith('https://'));
      expect(absoluteUrl, contains('/archives/123'));
    });
  });
}
