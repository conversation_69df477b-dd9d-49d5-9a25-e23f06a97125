import 'article.dart';

/// 搜索结果数据模型
class SearchResult {
  /// 搜索关键词
  final String query;
  
  /// 搜索到的文章列表
  final List<Article> articles;
  
  /// 当前页码
  final int currentPage;
  
  /// 总页数
  final int totalPages;
  
  /// 总结果数
  final int totalResults;
  
  /// 搜索建议
  final List<String> suggestions;
  
  /// 排序类型
  final String sortType;

  const SearchResult({
    required this.query,
    required this.articles,
    required this.currentPage,
    required this.totalPages,
    required this.totalResults,
    this.suggestions = const [],
    this.sortType = 'relevance',
  });

  /// 是否有更多页面
  bool get hasMorePages => currentPage < totalPages;

  /// 是否为第一页
  bool get isFirstPage => currentPage == 1;

  /// 是否为最后一页
  bool get isLastPage => currentPage >= totalPages;

  /// 是否有搜索结果
  bool get hasResults => articles.isNotEmpty;

  /// 是否有搜索建议
  bool get hasSuggestions => suggestions.isNotEmpty;

  /// 获取按数据源分组的文章
  Map<int, List<Article>> get articlesBySource {
    final Map<int, List<Article>> grouped = {};
    for (final article in articles) {
      grouped.putIfAbsent(article.sourceIndex, () => []).add(article);
    }
    return grouped;
  }

  /// 获取指定数据源的文章
  List<Article> getArticlesBySource(int sourceIndex) {
    return articles.where((article) => article.sourceIndex == sourceIndex).toList();
  }

  /// 获取所有数据源索引
  List<int> get availableSources {
    return articles.map((article) => article.sourceIndex).toSet().toList()..sort();
  }

  /// 复制并更新搜索结果
  SearchResult copyWith({
    String? query,
    List<Article>? articles,
    int? currentPage,
    int? totalPages,
    int? totalResults,
    List<String>? suggestions,
    String? sortType,
  }) {
    return SearchResult(
      query: query ?? this.query,
      articles: articles ?? this.articles,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalResults: totalResults ?? this.totalResults,
      suggestions: suggestions ?? this.suggestions,
      sortType: sortType ?? this.sortType,
    );
  }

  /// 合并搜索结果（用于分页加载）
  SearchResult merge(SearchResult other) {
    print('🔄 SearchResult.merge called');
    print('📊 Current: page=$currentPage, total=$totalPages, articles=${articles.length}');
    print('📊 Other: page=${other.currentPage}, total=${other.totalPages}, articles=${other.articles.length}');

    // 使用更大的totalPages值，确保分页信息正确
    final maxTotalPages = totalPages > other.totalPages ? totalPages : other.totalPages;

    final merged = SearchResult(
      query: query,
      articles: [...articles, ...other.articles],
      currentPage: other.currentPage,
      totalPages: maxTotalPages, // 使用最大的totalPages
      totalResults: articles.length + other.articles.length, // 使用实际文章数量
      suggestions: suggestions.isEmpty ? other.suggestions : suggestions,
      sortType: sortType,
    );

    print('📊 Merged: page=${merged.currentPage}, total=${merged.totalPages}, articles=${merged.articles.length}');
    print('📊 HasMorePages: ${merged.hasMorePages}');

    return merged;
  }

  @override
  String toString() {
    return 'SearchResult(query: $query, articles: ${articles.length}, currentPage: $currentPage, totalPages: $totalPages, totalResults: $totalResults)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is SearchResult &&
        other.query == query &&
        other.currentPage == currentPage &&
        other.totalPages == totalPages &&
        other.totalResults == totalResults &&
        other.sortType == sortType;
  }

  @override
  int get hashCode {
    return query.hashCode ^
        currentPage.hashCode ^
        totalPages.hashCode ^
        totalResults.hashCode ^
        sortType.hashCode;
  }
}

/// 搜索排序类型枚举
enum SearchSortType {
  relevance('relevance', '相关度'),
  popularity('popularity', '热度最高'),
  latest('latest', '最新发布');

  const SearchSortType(this.value, this.label);

  final String value;
  final String label;

  static SearchSortType fromValue(String value) {
    return SearchSortType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SearchSortType.relevance,
    );
  }
}
