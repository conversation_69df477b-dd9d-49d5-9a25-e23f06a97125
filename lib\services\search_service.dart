import 'package:flutter_chi_gua/utils/http_headers_util.dart';
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import '../models/article.dart';
import '../models/search_result.dart';
import 'settings_service.dart';

/// 搜索服务
/// 负责处理全站搜索功能
class SearchService {
  final SettingsService _settingsService = SettingsService.instance;

  /// 搜索文章（多数据源）
  /// [query] 搜索关键词
  /// [page] 页码，默认为1
  /// [sortType] 排序类型：'relevance'(相关度), 'popularity'(热度), 'latest'(最新)
  Future<SearchResult> searchArticles({
    required String query,
    int page = 1,
    String sortType = 'relevance',
  }) async {
    try {
      if (query.trim().isEmpty) {
        throw Exception('搜索关键词不能为空');
      }

      // 从多个数据源并行搜索
      final futures = <Future<SearchResult>>[];
      final baseUrls = _settingsService.allBaseUrls;

      for (int i = 0; i < baseUrls.length; i++) {
        futures.add(_searchFromSource(query, page, sortType, i));
      }

      // 等待所有搜索完成，但允许部分失败
      final results = await Future.wait(futures, eagerError: false);

      // 合并结果
      return _mergeSearchResults(results, query, page, sortType);
    } catch (e) {
      print('SearchService: Error searching articles: $e');
      throw Exception('搜索失败: $e');
    }
  }

  /// 从单个数据源搜索文章（公共方法）
  /// [query] 搜索关键词
  /// [page] 页码，默认为1
  /// [sortType] 排序类型
  /// [sourceIndex] 数据源索引
  Future<SearchResult> searchArticlesFromSource({
    required String query,
    int page = 1,
    String sortType = 'relevance',
    required int sourceIndex,
  }) async {
    try {
      if (query.trim().isEmpty) {
        throw Exception('搜索关键词不能为空');
      }

      return await _searchFromSource(query, page, sortType, sourceIndex);
    } catch (e) {
      print('SearchService: Error searching from source $sourceIndex: $e');
      throw Exception('从数据源搜索失败: $e');
    }
  }

  /// 从单个数据源搜索
  Future<SearchResult> _searchFromSource(String query, int page, String sortType, int sourceIndex) async {
    try {
      final baseUrls = _settingsService.allBaseUrls;
      if (sourceIndex >= baseUrls.length) {
        return SearchResult(
          query: query,
          articles: [],
          currentPage: page,
          totalPages: 1,
          totalResults: 0,
          suggestions: [],
          sortType: sortType,
        );
      }

      final baseUrl = baseUrls[sourceIndex];
      final encodedQuery = Uri.encodeComponent(query.trim());

      // 根据数据源索引使用不同的搜索URL格式
      String url;
      if (sourceIndex == 0) {
        // 数据源1：原有格式
        url = page == 1
            ? '$baseUrl/search/$encodedQuery/'
            : '$baseUrl/search/$encodedQuery/$page/';
      } else {
        // 数据源2：新格式 (baseurl2)
        url = '$baseUrl/vodsearch/-------------.html?wd=$encodedQuery&submit=';
        if (page > 1) {
          url = '$baseUrl/vodsearch/$encodedQuery----------$page---.html';
        }
      }

      print('SearchService: Searching for "$query" at URL: $url (source: ${sourceIndex + 1})');

      final response = await http.get(
        Uri.parse(url),
        headers: HttpHeadersUtil.getBasicHeaders(),
      );

      if (response.statusCode == 200) {
        return await _parseSearchResults(response.body, query, page, sourceIndex);
      } else {
        print('SearchService: HTTP error ${response.statusCode} for source $sourceIndex');
        return SearchResult(
          query: query,
          articles: [],
          currentPage: page,
          totalPages: 1,
          totalResults: 0,
          suggestions: [],
          sortType: sortType,
        );
      }
    } catch (e) {
      print('SearchService: Error searching from source $sourceIndex: $e');
      // 返回空结果而不是抛出异常，这样其他源仍能正常工作
      return SearchResult(
        query: query,
        articles: [],
        currentPage: page,
        totalPages: 1,
        totalResults: 0,
        suggestions: [],
        sortType: sortType,
      );
    }
  }

  /// 合并多个搜索结果
  SearchResult _mergeSearchResults(List<SearchResult> results, String query, int page, String sortType) {
    final allArticles = <Article>[];
    final allSuggestions = <String>[];
    int maxTotalPages = 1;

    for (final result in results) {
      allArticles.addAll(result.articles);
      allSuggestions.addAll(result.suggestions);
      if (result.totalPages > maxTotalPages) {
        maxTotalPages = result.totalPages;
      }
    }

    // 去重建议
    final uniqueSuggestions = allSuggestions.toSet().toList();

    // 根据排序类型对文章进行排序
    _sortArticles(allArticles, sortType);

    return SearchResult(
      query: query,
      articles: allArticles,
      currentPage: page,
      totalPages: maxTotalPages,
      totalResults: allArticles.length,
      suggestions: uniqueSuggestions,
      sortType: sortType,
    );
  }

  /// 对文章进行排序
  void _sortArticles(List<Article> articles, String sortType) {
    switch (sortType) {
      case 'latest':
        articles.sort((a, b) => b.publishDate.compareTo(a.publishDate));
        break;
      case 'popularity':
        // 这里可以根据实际的热度指标进行排序
        // 暂时按发布日期排序
        articles.sort((a, b) => b.publishDate.compareTo(a.publishDate));
        break;
      case 'relevance':
      default:
        // 相关度排序可以根据标题匹配度等进行
        // 暂时保持原有顺序
        break;
    }
  }

  /// 根据数据源过滤搜索结果
  SearchResult filterResultsBySource(SearchResult originalResult, int sourceIndex) {
    final filteredArticles = originalResult.getArticlesBySource(sourceIndex);

    print('🔍 FilterResultsBySource called');
    print('📊 Original: page=${originalResult.currentPage}, total=${originalResult.totalPages}, articles=${originalResult.articles.length}');
    print('📊 Source $sourceIndex articles: ${filteredArticles.length}');

    // 对于单个数据源，我们需要更合理地评估分页信息
    // 策略：保持原始结果的totalPages，因为单个数据源的分页应该与原始搜索一致
    // 只有当过滤后没有文章时，才认为没有更多页面
    int estimatedTotalPages = originalResult.totalPages;

    // 如果当前页没有来自该数据源的文章，但原始结果有更多页，仍然保持原有的totalPages
    // 这样可以确保即使当前页某个数据源没有结果，下一页仍可能有结果
    if (filteredArticles.isEmpty && originalResult.currentPage >= originalResult.totalPages) {
      // 只有在当前页已经是最后一页且没有文章时，才设置为当前页
      estimatedTotalPages = originalResult.currentPage;
    }

    final filtered = SearchResult(
      query: originalResult.query,
      articles: filteredArticles,
      currentPage: originalResult.currentPage,
      totalPages: estimatedTotalPages,
      totalResults: filteredArticles.length,
      suggestions: originalResult.suggestions,
      sortType: originalResult.sortType,
    );

    print('📊 Filtered result: page=${filtered.currentPage}, total=${filtered.totalPages}, articles=${filtered.articles.length}');
    print('📊 HasMorePages: ${filtered.hasMorePages}');

    return filtered;
  }

  /// 解析搜索结果页面
  Future<SearchResult> _parseSearchResults(String html, String query, int page, [int sourceIndex = 0]) async {
    try {
      final document = html_parser.parse(html);

      if (sourceIndex == 0) {
        // 数据源1：原有解析逻辑
        return await _parseSource1Results(document, query, page, sourceIndex);
      } else {
        // 数据源2：新的解析逻辑 (baseurl2)
        return await _parseSource2Results(document, query, page, sourceIndex);
      }
    } catch (e) {
      print('SearchService: Error parsing search results: $e');
      throw Exception('解析搜索结果失败: $e');
    }
  }

  /// 解析数据源1的搜索结果
  Future<SearchResult> _parseSource1Results(Document document, String query, int page, int sourceIndex) async {
    try {
      final articles = <Article>[];

      // 查找文章列表
      final articleElements = document.querySelectorAll('article');

      for (final article in articleElements) {
        try {
          // 提取标题和链接
          final linkElement = article.querySelector('a[href*="/archives/"]');
          if (linkElement == null) continue;

          final href = linkElement.attributes['href'];
          if (href == null || href.isEmpty) continue;

          final titleElement = article.querySelector('h2');
          if (titleElement == null) continue;

          final title = titleElement.text.trim();
          if (title.isEmpty) continue;

          // 构建完整URL
          final fullUrl = _settingsService.getFullUrlForSource(href, sourceIndex);

          // 提取文章ID
          final idMatch = RegExp(r'/archives/(\d+)/').firstMatch(href);
          final id = idMatch?.group(1) ?? '';

          if (id.isEmpty) continue;

          // 提取发布日期和分类信息
          String publishDateStr = '';
          List<String> categories = [];
          String author = '';

          // 查找所有generic元素，包括嵌套的
          final allGenericElements = article.querySelectorAll('generic');

          for (final meta in allGenericElements) {
            final text = meta.text.trim();

            // 检查是否是日期元素
            if (text.contains('年') && text.contains('月') && text.contains('日')) {
              // 移除可能的 • 符号
              final cleanText = text.replaceAll('•', '').trim();
              // 确保这是一个纯日期字符串，不包含其他信息
              if (RegExp(r'^\d{4}\s*年\s*\d{1,2}\s*月\s*\d{1,2}\s*日\s*$').hasMatch(cleanText)) {
                publishDateStr = cleanText;
              }
            }
            // 检查是否是分类信息
            else if (text.contains(',') && !text.contains('年') && !text.contains('•')) {
              categories = text.split(',').map((cat) => cat.trim()).toList();
            }
            // 检查是否是作者信息
            else if (text.contains('•') && !text.contains('年') && !text.contains(',')) {
              // 提取作者信息，格式如："瓜农 •"
              author = text.replaceAll('•', '').trim();
            }
          }

          // 解析发布日期
          DateTime publishDate = DateTime.now();

          if (publishDateStr.isNotEmpty) {
            try {
              // 解析中文日期格式：2025 年 05 月 04 日
              final datePattern = r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日';
              final dateMatch = RegExp(datePattern).firstMatch(publishDateStr);

              if (dateMatch != null) {
                final year = int.parse(dateMatch.group(1)!);
                final month = int.parse(dateMatch.group(2)!);
                final day = int.parse(dateMatch.group(3)!);
                publishDate = DateTime(year, month, day);
              }
            } catch (e) {
              // 如果解析失败，使用当前时间
              publishDate = DateTime.now();
            }
          }

          // 尝试提取文章封面图片（使用与首页相同的逻辑）
          String? imageUrl;
          print('🖼️ Image extraction for article "$title":');

          // 方法1：从script标签中提取图片URL（与首页逻辑一致）
          final scriptElements = article.querySelectorAll('script');
          for (final script in scriptElements) {
            final scriptText = script.text;
            if (scriptText.contains('loadBannerDirect')) {
              print('   - Found loadBannerDirect script');
              // 使用正则表达式提取图片URL
              final imageUrlRegex = RegExp(r"'(https://pic.*?)'");
              final match = imageUrlRegex.firstMatch(scriptText);
              if (match != null) {
                imageUrl = match.group(1);
                print('   - Extracted image URL from script: $imageUrl');
                break;
              }
            }
          }

          // 方法2：如果script方法失败，尝试传统的img元素
          if (imageUrl == null) {
            final imgElement = article.querySelector('img');
            print('   - Fallback to img element: ${imgElement != null}');

            if (imgElement != null) {
              final src = imgElement.attributes['src'];
              print('   - Image src attribute: $src');

              if (src != null && src.isNotEmpty) {
                imageUrl = _settingsService.getFullUrlForSource(src, sourceIndex);
                print('   - Full image URL from img: $imageUrl');
              }
            }
          }

          print('   - Final imageUrl: $imageUrl');

          // 创建文章对象
          final articleObj = Article(
            id: id,
            title: title,
            url: fullUrl,
            excerpt: '', // 搜索结果页面通常没有摘要
            author: author,
            publishDate: publishDate,
            categories: categories,
            imageUrl: imageUrl, // 使用提取的图片URL
            content: '',
            contentBlocks: [],
            sourceIndex: sourceIndex,
            sourceName: _settingsService.getSourceName(sourceIndex),
          );

          articles.add(articleObj);
        } catch (e) {
          print('SearchService: Error parsing article: $e');
          continue;
        }
      }

      // 提取分页信息
      int totalPages = 1;

      // 查找分页信息，格式如 "1/16" 或 "2/16"
      final paginationElements = document.querySelectorAll('generic');
      for (final element in paginationElements) {
        final text = element.text.trim();
        // 匹配 "当前页/总页数" 格式
        final pageMatch = RegExp(r'^(\d+)/(\d+)$').firstMatch(text);
        if (pageMatch != null) {
          final currentPageFromHtml = int.tryParse(pageMatch.group(1) ?? '1') ?? 1;
          totalPages = int.tryParse(pageMatch.group(2) ?? '1') ?? 1;

          // 验证当前页码是否匹配
          if (currentPageFromHtml == page) {
            break;
          }
        }
      }

      // 如果没有找到分页信息，检查是否有"下一页"链接来判断是否还有更多页面
      if (totalPages == 1) {
        // 检查多种可能的下一页链接格式
        bool hasNextPage = false;

        // 方法1：查找包含下一页页码的链接
        final nextPageLinks = document.querySelectorAll('a[href*="/search/"]');
        for (final link in nextPageLinks) {
          final href = link.attributes['href'] ?? '';
          if (href.contains('/${page + 1}/')) {
            hasNextPage = true;
            break;
          }
        }

        // 方法2：查找"下一页"或">"文本的链接
        if (!hasNextPage) {
          final navigationLinks = document.querySelectorAll('a');
          for (final link in navigationLinks) {
            final text = link.text.trim();
            final href = link.attributes['href'] ?? '';
            if ((text.contains('下一页') || text.contains('>') || text.contains('Next')) &&
                href.contains('/search/')) {
              hasNextPage = true;
              break;
            }
          }
        }

        // 方法3：检查当前页是否有足够的文章（如果文章数量很少，可能是最后一页）
        if (!hasNextPage && articles.length >= 10) {
          // 如果当前页有足够多的文章（>=10），假设可能还有下一页
          hasNextPage = true;
        }

        if (hasNextPage) {
          totalPages = page + 1; // 至少还有下一页
        } else {
          totalPages = page; // 当前页就是最后一页
        }

        print('SearchService: Page $page, found ${articles.length} articles, hasNextPage: $hasNextPage, totalPages: $totalPages');
      }

      // 提取相关搜索建议
      final suggestions = <String>[];
      final suggestionElements = document.querySelectorAll('region[aria-label="猜你想搜"] a');
      for (final suggestion in suggestionElements) {
        final text = suggestion.text.trim();
        if (text.isNotEmpty && text != query) {
          suggestions.add(text);
        }
      }

      // 搜索完成

      return SearchResult(
        query: query,
        articles: articles,
        currentPage: page,
        totalPages: totalPages,
        totalResults: articles.length,
        suggestions: suggestions,
      );
    } catch (e) {
      print('SearchService: Error parsing source 1 results: $e');
      throw Exception('解析数据源1搜索结果失败: $e');
    }
  }

  /// 解析数据源2的搜索结果 (baseurl2)
  Future<SearchResult> _parseSource2Results(Document document, String query, int page, int sourceIndex) async {
    try {
      final articles = <Article>[];

      // 查找搜索结果列表 - baseurl2使用不同的HTML结构
      final resultElements = document.querySelectorAll('li');

      for (final listItem in resultElements) {
        try {
          // 查找视频链接
          final linkElement = listItem.querySelector('a[href*="/vodplay/"]');
          if (linkElement == null) continue;

          final href = linkElement.attributes['href'];
          if (href == null || href.isEmpty) continue;

          // 查找标题
          final titleElement = listItem.querySelector('h4 a');
          if (titleElement == null) continue;

          final title = titleElement.text.trim();
          if (title.isEmpty) continue;

          // 构建完整URL
          final fullUrl = _settingsService.getFullUrlForSource(href, sourceIndex);

          // 提取文章ID - baseurl2格式：/vodplay/67725-1-1.html
          final idMatch = RegExp(r'/vodplay/(\d+)-').firstMatch(href);
          final id = idMatch?.group(1) ?? '';

          if (id.isEmpty) continue;

          // 提取时长信息
          String duration = '';
          final durationElement = listItem.querySelector('generic');
          if (durationElement != null) {
            duration = durationElement.text.trim();
          }

          // 提取发布日期 - 从页面中查找日期信息
          DateTime publishDate = DateTime.now();
          final dateElements = listItem.querySelectorAll('text');
          for (final dateElement in dateElements) {
            final text = dateElement.text.trim();
            // 匹配日期格式：07-02, 06-04等
            final dateMatch = RegExp(r'(\d{2})-(\d{2})').firstMatch(text);
            if (dateMatch != null) {
              try {
                final month = int.parse(dateMatch.group(1)!);
                final day = int.parse(dateMatch.group(2)!);
                final currentYear = DateTime.now().year;
                publishDate = DateTime(currentYear, month, day);
                break;
              } catch (e) {
                // 如果解析失败，使用当前时间
                publishDate = DateTime.now();
              }
            }
          }

          // 创建文章对象
          final articleObj = Article(
            id: id,
            title: title,
            url: fullUrl,
            excerpt: duration.isNotEmpty ? '时长: $duration' : '', // 使用时长作为摘要
            author: '', // baseurl2通常没有作者信息
            publishDate: publishDate,
            categories: ['视频'], // 默认分类
            imageUrl: null, // baseurl2搜索结果页面通常没有缩略图
            content: '',
            contentBlocks: [],
            sourceIndex: sourceIndex,
            sourceName: _settingsService.getSourceName(sourceIndex),
          );

          articles.add(articleObj);
        } catch (e) {
          print('SearchService: Error parsing baseurl2 article: $e');
          continue;
        }
      }

      // 提取分页信息 - baseurl2的分页结构
      int totalPages = 1;
      final paginationElements = document.querySelectorAll('a');
      for (final element in paginationElements) {
        final text = element.text.trim();
        final href = element.attributes['href'] ?? '';

        // 查找"下一页"链接来判断是否还有更多页面
        if (text.contains('下一页') && href.isNotEmpty) {
          totalPages = page + 1; // 至少还有下一页
          break;
        }
      }

      // 如果没有找到下一页链接，检查当前页文章数量
      if (totalPages == 1 && articles.length >= 15) {
        // 如果当前页有足够多的文章，假设可能还有下一页
        totalPages = page + 1;
      } else if (totalPages == 1) {
        totalPages = page; // 当前页就是最后一页
      }

      print('SearchService: Source 2 - Page $page, found ${articles.length} articles, totalPages: $totalPages');

      return SearchResult(
        query: query,
        articles: articles,
        currentPage: page,
        totalPages: totalPages,
        totalResults: articles.length,
        suggestions: [], // baseurl2搜索结果页面通常没有搜索建议
      );
    } catch (e) {
      print('SearchService: Error parsing source 2 results: $e');
      throw Exception('解析数据源2搜索结果失败: $e');
    }
  }

  /// 获取搜索建议
  /// 基于用户输入的部分关键词返回搜索建议
  Future<List<String>> getSearchSuggestions(String partialQuery) async {
    try {
      if (partialQuery.trim().isEmpty) {
        return [];
      }

      // 这里可以实现搜索建议的逻辑
      // 目前返回一些常见的搜索关键词
      final commonKeywords = [
        '明星', '网红', '学生', '校园', '娱乐', '大瓜', 
        '黑料', '爆料', '视频', '图片', '合集', '最新'
      ];

      final query = partialQuery.toLowerCase();
      return commonKeywords
          .where((keyword) => keyword.toLowerCase().contains(query))
          .take(5)
          .toList();
    } catch (e) {
      print('SearchService: Error getting search suggestions: $e');
      return [];
    }
  }

  /// 清理搜索历史
  Future<void> clearSearchHistory() async {
    // 这里可以实现清理搜索历史的逻辑
    // 目前暂不实现
  }

  /// 获取热门搜索关键词
  Future<List<String>> getHotSearchKeywords() async {
    try {
      // 这里可以从网站首页或专门的接口获取热门搜索关键词
      // 目前返回一些预定义的热门关键词
      return [
        '明星黑料',
        '网红爆料', 
        '学生校园',
        '热门大瓜',
        '最新视频',
        '娱乐八卦',
        '海外吃瓜',
        '原创内容'
      ];
    } catch (e) {
      print('SearchService: Error getting hot search keywords: $e');
      return [];
    }
  }
}
