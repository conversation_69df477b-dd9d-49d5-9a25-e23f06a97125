import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import '../models/category.dart';
import 'settings_service.dart';
import '../utils/http_headers_util.dart';

/// 分类服务
/// 负责获取和管理分类信息
class CategoryService {
  final SettingsService _settingsService = SettingsService.instance;
  
  /// 预定义的分类列表
  static const List<Map<String, String>> _predefinedCategories = [
    {'id': 'jrcg', 'name': '今日吃瓜', 'slug': 'jrcg'},
    {'id': 'cgbd', 'name': '吃瓜榜单', 'slug': 'cgbd'},
    {'id': 'xsxy', 'name': '学生校园', 'slug': 'xsxy'},
    {'id': 'rmdg', 'name': '热门大瓜', 'slug': 'rmdg'},
    {'id': 'kpyyl', 'name': '看片y娱乐', 'slug': 'kpyyl'},
    {'id': 'bkdg', 'name': '必看大瓜', 'slug': 'bkdg'},
    {'id': 'lldd', 'name': '伦理道德', 'slug': 'lldd'},
    {'id': 'whhl', 'name': '网红黑料', 'slug': 'whhl'},
    {'id': 'hwcg', 'name': '海外吃瓜', 'slug': 'hwcg'},
    {'id': 'mxhl', 'name': '明星黑料', 'slug': 'mxhl'},
    {'id': 'snsn', 'name': '骚男骚女', 'slug': 'snsn'},
    {'id': 'mrds', 'name': '每日大赛', 'slug': 'mrds'},
    {'id': 'cbls', 'name': '擦边撩骚', 'slug': 'cbls'},
    {'id': '51zzs', 'name': '51涨知识', 'slug': '51zzs'},
    {'id': 'rrcg', 'name': '人人吃瓜', 'slug': 'rrcg'},
    {'id': 'ldgb', 'name': '领导干部', 'slug': 'ldgb'},
    {'id': '51jc', 'name': '51剧场', 'slug': '51jc'},
    {'id': 'cgxw', 'name': '吃瓜新闻', 'slug': 'cgxw'},
    {'id': 'mfdj', 'name': '免费短剧', 'slug': 'mfdj'},
    {'id': '51yc', 'name': '51原创', 'slug': '51yc'},
    {'id': 'cgkx', 'name': '吃瓜看戏', 'slug': 'cgkx'},
  ];

  /// 获取所有分类
  Future<List<Category>> getCategories() async {
    try {
      // 首先获取网站的分类页面
      final categoriesFromWeb = await _fetchCategoriesFromWeb();
      
      // 如果网络获取失败，使用预定义分类
      if (categoriesFromWeb.isEmpty) {
        return _getPredefinedCategories();
      }
      
      // 合并网络分类和预定义分类
      return _mergeCategoriesWithPredefined(categoriesFromWeb);
    } catch (e) {
      print('CategoryService: Error fetching categories: $e');
      // 出错时返回预定义分类
      return _getPredefinedCategories();
    }
  }

  /// 从网站获取分类
  Future<List<Category>> _fetchCategoriesFromWeb() async {
    try {
      final baseUrl = _settingsService.baseUrl;
      final response = await http.get(
        Uri.parse(baseUrl),
        headers: HttpHeadersUtil.getBasicHeaders(),
      );

      if (response.statusCode == 200) {
        return _parseCategoriesFromHtml(response.body);
      } else {
        print('CategoryService: HTTP error ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('CategoryService: Network error: $e');
      return [];
    }
  }

  /// 从HTML解析分类
  List<Category> _parseCategoriesFromHtml(String html) {
    try {
      final document = html_parser.parse(html);
      final categories = <Category>[];
      final addedIds = <String>{}; // 用于去重

      // 查找导航菜单中的分类链接
      final navLinks = document.querySelectorAll('nav a, .menu a, .category-link a');

      for (final link in navLinks) {
        final href = link.attributes['href'];
        final text = link.text.trim();

        if (href != null && text.isNotEmpty && href.contains('/category/')) {
          // 提取分类slug
          final slugMatch = RegExp(r'/category/([^/]+)/?').firstMatch(href);
          if (slugMatch != null) {
            final slug = slugMatch.group(1)!;

            // 检查是否已经添加过这个分类（去重）
            if (!addedIds.contains(slug)) {
              final fullUrl = _settingsService.getFullUrl(href);

              categories.add(Category(
                id: slug,
                name: text,
                url: fullUrl,
                isAvailable: true,
              ));

              addedIds.add(slug);
            }
          }
        }
      }

      print('CategoryService: Parsed ${categories.length} unique categories from web');
      return categories;
    } catch (e) {
      print('CategoryService: Error parsing categories: $e');
      return [];
    }
  }

  /// 获取预定义分类
  List<Category> _getPredefinedCategories() {
    return _predefinedCategories.map((categoryData) {
      final baseUrl = _settingsService.baseUrl;
      final url = '$baseUrl/category/${categoryData['slug']}/';
      
      return Category(
        id: categoryData['id']!,
        name: categoryData['name']!,
        url: url,
        isAvailable: true,
      );
    }).toList();
  }

  /// 合并网络分类和预定义分类
  List<Category> _mergeCategoriesWithPredefined(List<Category> webCategories) {
    final predefinedCategories = _getPredefinedCategories();
    final mergedCategories = <Category>[];
    final addedIds = <String>{};
    final addedNames = <String>{}; // 添加名称去重

    // 首先添加网络获取的分类
    for (final category in webCategories) {
      if (!addedIds.contains(category.id) && !addedNames.contains(category.name.toLowerCase())) {
        mergedCategories.add(category);
        addedIds.add(category.id);
        addedNames.add(category.name.toLowerCase());
      }
    }

    // 然后添加预定义分类中未包含的项目
    for (final predefined in predefinedCategories) {
      if (!addedIds.contains(predefined.id) && !addedNames.contains(predefined.name.toLowerCase())) {
        mergedCategories.add(predefined);
        addedIds.add(predefined.id);
        addedNames.add(predefined.name.toLowerCase());
      }
    }

    print('CategoryService: Merged ${mergedCategories.length} unique categories (${webCategories.length} from web, ${predefinedCategories.length} predefined)');
    return mergedCategories;
  }

  /// 验证分类URL是否可访问
  Future<bool> validateCategoryUrl(String url) async {
    try {
      final response = await http.head(
        Uri.parse(url),
        headers: HttpHeadersUtil.getUserAgentHeader(),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('CategoryService: Error validating URL $url: $e');
      return false;
    }
  }

  /// 根据分类ID获取分类
  Category? getCategoryById(String id) {
    final predefined = _predefinedCategories.firstWhere(
      (cat) => cat['id'] == id,
      orElse: () => <String, String>{},
    );
    
    if (predefined.isNotEmpty) {
      final baseUrl = _settingsService.baseUrl;
      final url = '$baseUrl/category/${predefined['slug']}/';
      
      return Category(
        id: predefined['id']!,
        name: predefined['name']!,
        url: url,
        isAvailable: true,
      );
    }
    
    return null;
  }

  /// 搜索分类
  List<Category> searchCategories(List<Category> categories, String query) {
    if (query.isEmpty) return categories;
    
    final lowerQuery = query.toLowerCase();
    return categories.where((category) {
      return category.name.toLowerCase().contains(lowerQuery) ||
             category.id.toLowerCase().contains(lowerQuery);
    }).toList();
  }
}
