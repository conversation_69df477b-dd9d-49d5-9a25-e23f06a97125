<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变定义 -->
  <defs>
    <!-- 背景渐变：温暖的橙色调 -->
    <radialGradient id="bgGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FF7043;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF5722;stop-opacity:1" />
    </radialGradient>
    
    <!-- 瓜子渐变：深棕色调 -->
    <linearGradient id="seedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8D4E2A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5D2F1A;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="60" cy="60" r="55" fill="url(#bgGradient)" stroke="#E64A19" stroke-width="2" filter="url(#shadow)"/>
  
  <!-- 瓜子主体 - 椭圆形，略微旋转 -->
  <ellipse cx="60" cy="60" rx="35" ry="20" fill="url(#seedGradient)" transform="rotate(-15 60 60)"/>
  
  <!-- 瓜子纹理线条 -->
  <path d="M35 55 Q60 50 85 55" stroke="#3E1A0A" stroke-width="1.5" fill="none" opacity="0.6"/>
  <path d="M38 62 Q60 58 82 62" stroke="#3E1A0A" stroke-width="1.5" fill="none" opacity="0.6"/>
  <path d="M40 68 Q60 65 80 68" stroke="#3E1A0A" stroke-width="1" fill="none" opacity="0.4"/>
  
  <!-- 眼睛外圈 -->
  <circle cx="60" cy="60" r="13" fill="white" stroke="#E0E0E0" stroke-width="1.5"/>
  
  <!-- 眼睛瞳孔 -->
  <circle cx="60" cy="60" r="8" fill="#2E2E2E"/>
  
  <!-- 瞳孔内圈 -->
  <circle cx="60" cy="60" r="5" fill="#1A1A1A"/>
  
  <!-- 眼睛高光 - 主高光 -->
  <circle cx="57" cy="57" r="2.5" fill="white" opacity="0.9"/>
  
  <!-- 眼睛高光 - 次高光 -->
  <circle cx="62" cy="61" r="1" fill="white" opacity="0.7"/>
  
  <!-- 装饰性小点 -->
  <circle cx="64" cy="56" r="0.5" fill="white" opacity="0.5"/>
</svg>
