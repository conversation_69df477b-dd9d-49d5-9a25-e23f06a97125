import 'package:flutter/services.dart';

/// 视频启动器服务
/// 用于调用系统播放器播放视频，特别是处理 m3u8 格式
class VideoLauncherService {
  static const MethodChannel _channel = MethodChannel('com.flutter.chi.gua/video_launcher');

  /// 使用系统播放器播放视频
  /// 
  /// [videoUrl] 视频URL
  /// 返回是否成功启动播放器
  static Future<bool> launchVideoPlayer(String videoUrl) async {
    try {
      final result = await _channel.invokeMethod('launchVideoPlayer', {
        'url': videoUrl,
      });
      return result == true;
    } on PlatformException catch (e) {
      // 处理平台异常
      switch (e.code) {
        case 'NO_APP':
          throw VideoLauncherException('没有找到可以播放此视频的应用');
        case 'LAUNCH_ERROR':
          throw VideoLauncherException('启动播放器失败: ${e.message}');
        case 'INVALID_ARGUMENT':
          throw VideoLauncherException('无效的视频URL');
        default:
          throw VideoLauncherException('未知错误: ${e.message}');
      }
    } catch (e) {
      throw VideoLauncherException('调用系统播放器失败: $e');
    }
  }
}

/// 视频启动器异常
class VideoLauncherException implements Exception {
  final String message;
  
  const VideoLauncherException(this.message);
  
  @override
  String toString() => 'VideoLauncherException: $message';
}
