#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行文章分析的简单脚本
"""

from analyze_articles import ArticleAnalyzer
import sys

def main():
    print("🚀 启动文章视频分析工具")
    print("="*50)
    
    analyzer = ArticleAnalyzer()
    
    # 首先获取并显示文章列表
    print("📋 正在获取首页文章列表...")
    articles = analyzer.fetch_articles_from_homepage()
    
    if not articles:
        print("❌ 没有找到文章")
        return
    
    print(f"✅ 找到 {len(articles)} 篇文章:")
    print()
    
    for i, article in enumerate(articles, 1):
        print(f"{i:2d}. {article['title']}")
        print(f"    📄 ID: {article['id']}")
        print(f"    🔗 URL: {article['url']}")
        if article['image_url']:
            print(f"    🖼️  图片: {article['image_url']}")
        print()
    
    # 询问用户要分析哪些文章
    print("请选择要分析的文章:")
    print("1. 分析前3篇文章")
    print("2. 分析所有文章")
    print("3. 选择特定文章")
    print("4. 只显示URL列表")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == '1':
        # 分析前3篇
        articles_to_analyze = articles[:3]
        print(f"\n🔍 将分析前3篇文章")
        
    elif choice == '2':
        # 分析所有文章
        articles_to_analyze = articles
        print(f"\n🔍 将分析所有 {len(articles)} 篇文章")
        
    elif choice == '3':
        # 选择特定文章
        try:
            indices = input("请输入文章编号（用逗号分隔，如: 1,3,5): ").strip()
            selected_indices = [int(x.strip()) - 1 for x in indices.split(',')]
            articles_to_analyze = [articles[i] for i in selected_indices if 0 <= i < len(articles)]
            print(f"\n🔍 将分析选中的 {len(articles_to_analyze)} 篇文章")
        except (ValueError, IndexError):
            print("❌ 输入格式错误，将分析前3篇文章")
            articles_to_analyze = articles[:3]
            
    elif choice == '4':
        # 只显示URL列表
        print("\n📋 文章URL列表:")
        for i, article in enumerate(articles, 1):
            print(f"{i:2d}. {article['url']}")
        return
        
    else:
        print("❌ 无效选择，将分析前3篇文章")
        articles_to_analyze = articles[:3]
    
    # 开始分析
    print(f"\n🎬 开始视频分析...")
    results = []
    
    for i, article in enumerate(articles_to_analyze, 1):
        print(f"\n📊 进度: {i}/{len(articles_to_analyze)}")
        result = analyzer.analyze_article_videos(article)
        
        if result:
            results.append({
                'article': article,
                'analysis': result
            })
            
            # 显示这篇文章的视频摘要
            videos = result.get('videos', [])
            if videos:
                print(f"✅ 在《{article['title']}》中找到 {len(videos)} 个视频:")
                for j, video in enumerate(videos, 1):
                    print(f"   {j}. {video.get('src', 'N/A')} ({video.get('type', 'unknown')})")
            else:
                print(f"ℹ️  《{article['title']}》中没有找到视频")
        
        # 添加延时
        if i < len(articles_to_analyze):
            print("⏳ 等待2秒...")
            import time
            time.sleep(2)
    
    # 显示最终统计
    print(f"\n🎯 分析完成！")
    print(f"📈 统计结果:")
    
    total_videos = sum(len(r['analysis']['videos']) for r in results)
    total_images = sum(len(r['analysis']['images']) for r in results)
    
    print(f"   分析文章数: {len(results)}")
    print(f"   发现视频数: {total_videos}")
    print(f"   发现图片数: {total_images}")
    
    if total_videos > 0:
        print(f"\n🎥 所有视频链接:")
        video_count = 0
        for result in results:
            for video in result['analysis']['videos']:
                video_count += 1
                print(f"   {video_count}. {video.get('src', 'N/A')}")
                if video.get('platform'):
                    print(f"      平台: {video['platform']}")
    
    print(f"\n💾 详细结果已保存到各个JSON文件中")

if __name__ == '__main__':
    main()
