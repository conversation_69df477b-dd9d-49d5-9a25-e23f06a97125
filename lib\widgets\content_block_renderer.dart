import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/content_block.dart';
import '../models/article.dart';
import '../models/category.dart';
import '../screens/vertical_image_gallery_screen.dart';
import '../screens/article_detail_screen.dart';
import '../screens/category_articles_screen.dart';
import '../services/settings_service.dart';

import 'encrypted_network_image.dart';
import 'fijk_video_player_widget.dart';

/// 内容块渲染器
/// 将ContentBlock渲染为Flutter Widget
class ContentBlockRenderer extends StatelessWidget {
  final ContentBlock block;
  final VoidCallback? onLinkTap;
  final List<String>? allImageUrls; // 所有图片URL列表，用于画廊模式
  final int? imageIndex; // 当前图片在列表中的索引

  const ContentBlockRenderer({
    super.key,
    required this.block,
    this.onLinkTap,
    this.allImageUrls,
    this.imageIndex,
  });

  @override
  Widget build(BuildContext context) {
    switch (block.type) {
      case ContentBlockType.paragraph:
        return _buildParagraph(context, block as ParagraphBlock);
      case ContentBlockType.heading:
        return _buildHeading(context, block as HeadingBlock);
      case ContentBlockType.image:
        return _buildImage(context, block as ImageBlock);
      case ContentBlockType.video:
        return _buildVideo(context, block as VideoBlock);
      case ContentBlockType.audio:
        return _buildAudio(context, block as AudioBlock);
      case ContentBlockType.link:
        return _buildLink(context, block as LinkBlock);
      case ContentBlockType.blockquote:
        return _buildBlockquote(context, block as BlockquoteBlock);
      case ContentBlockType.codeBlock:
        return _buildCodeBlock(context, block as CodeBlock);
      case ContentBlockType.inlineCode:
        return _buildInlineCode(context, block as InlineCodeBlock);
      case ContentBlockType.orderedList:
        return _buildOrderedList(context, block as OrderedListBlock);
      case ContentBlockType.unorderedList:
        return _buildUnorderedList(context, block as UnorderedListBlock);
      case ContentBlockType.listItem:
        return _buildListItem(context, block as ListItemBlock);
      case ContentBlockType.divider:
        return _buildDivider(context, block as DividerBlock);
      case ContentBlockType.lineBreak:
        return _buildLineBreak();
      case ContentBlockType.bold:
        return _buildBold(context, block as BoldBlock);
      case ContentBlockType.italic:
        return _buildItalic(context, block as ItalicBlock);
      case ContentBlockType.mixed:
        return _buildMixed(context, block as MixedBlock);
      case ContentBlockType.embed:
        return _buildEmbed(context, block as EmbedBlock);
    }
  }

  /// 创建子内容块渲染器，传递图片列表信息
  Widget _buildChildRenderer(ContentBlock childBlock) {
    if (childBlock is ImageBlock && allImageUrls != null) {
      // 为图片块计算正确的索引
      final childImageIndex = allImageUrls!.indexOf(childBlock.src);
      return ContentBlockRenderer(
        block: childBlock,
        allImageUrls: allImageUrls,
        imageIndex: childImageIndex >= 0 ? childImageIndex : null,
        onLinkTap: onLinkTap,
      );
    }

    // 为非图片块传递图片列表，以支持嵌套图片
    return ContentBlockRenderer(
      block: childBlock,
      allImageUrls: allImageUrls,
      onLinkTap: onLinkTap,
    );
  }

  /// 构建段落
  Widget _buildParagraph(BuildContext context, ParagraphBlock block) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: SelectableText(
        block.text,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontSize: 16,
          height: 1.6,
        ),
      ),
    );
  }

  /// 构建标题
  Widget _buildHeading(BuildContext context, HeadingBlock block) {
    TextStyle? style;
    double topMargin = 24;
    double bottomMargin = 16;

    switch (block.level) {
      case HeadingLevel.h1:
        style = Theme.of(context).textTheme.headlineLarge?.copyWith(
          fontWeight: FontWeight.bold,
        );
        break;
      case HeadingLevel.h2:
        style = Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
        );
        break;
      case HeadingLevel.h3:
        style = Theme.of(context).textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.bold,
        );
        break;
      case HeadingLevel.h4:
        style = Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        );
        break;
      case HeadingLevel.h5:
        style = Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        );
        break;
      case HeadingLevel.h6:
        style = Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
        );
        break;
    }

    return Padding(
      padding: EdgeInsets.only(top: topMargin, bottom: bottomMargin),
      child: SelectableText(
        block.text,
        style: style,
      ),
    );
  }

  /// 构建图片
  Widget _buildImage(BuildContext context, ImageBlock block) {
    print('ContentBlockRenderer: Rendering image with URL: ${block.src}');

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: GestureDetector(
        onTap: () => _handleImageTap(context, block),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: EncryptedNetworkImage(
                imageUrl: block.src,
                width: double.infinity,
                fit: BoxFit.cover,
                errorWidget: Container(
                  height: 200,
                  color: Colors.grey[300],
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.broken_image,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        block.alt ?? '图片加载失败',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'URL: ${block.src}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                          fontSize: 10,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
        ),
      ),
    );
  }

  /// 构建链接
  Widget _buildLink(BuildContext context, LinkBlock block) {
    return GestureDetector(
      onTap: () => _handleLinkTap(context, block.href),
      child: Text(
        block.text,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }

  /// 构建引用块
  Widget _buildBlockquote(BuildContext context, BlockquoteBlock block) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.only(left: 16, top: 8, bottom: 8, right: 8),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 4,
          ),
        ),
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(4),
          bottomRight: Radius.circular(4),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: block.children
            .map((child) => _buildChildRenderer(child))
            .toList(),
      ),
    );
  }

  /// 构建代码块
  Widget _buildCodeBlock(BuildContext context, CodeBlock block) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: SelectableText(
        block.code,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontFamily: 'monospace',
          fontSize: 14,
        ),
      ),
    );
  }

  /// 构建行内代码
  Widget _buildInlineCode(BuildContext context, InlineCodeBlock block) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        block.code,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontFamily: 'monospace',
        ),
      ),
    );
  }

  /// 构建有序列表
  Widget _buildOrderedList(BuildContext context, OrderedListBlock block) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: block.items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final number = (block.start ?? 1) + index;
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 24,
                  child: Text(
                    '$number.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: item.children
                        .map((child) => _buildChildRenderer(child))
                        .toList(),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建无序列表
  Widget _buildUnorderedList(BuildContext context, UnorderedListBlock block) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: block.items.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 24,
                  child: Text(
                    '•',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: item.children
                        .map((child) => _buildChildRenderer(child))
                        .toList(),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建列表项
  Widget _buildListItem(BuildContext context, ListItemBlock block) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: block.children
          .map((child) => _buildChildRenderer(child))
          .toList(),
    );
  }

  /// 构建分割线
  Widget _buildDivider(BuildContext context, DividerBlock block) {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Divider(),
    );
  }

  /// 构建换行
  Widget _buildLineBreak() {
    return const SizedBox(height: 8);
  }

  /// 构建粗体文本
  Widget _buildBold(BuildContext context, BoldBlock block) {
    return Text(
      block.text,
      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// 构建斜体文本
  Widget _buildItalic(BuildContext context, ItalicBlock block) {
    return Text(
      block.text,
      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
        fontStyle: FontStyle.italic,
      ),
    );
  }

  /// 构建混合内容
  Widget _buildMixed(BuildContext context, MixedBlock block) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Wrap(
        children: block.children
            .map((child) => _buildChildRenderer(child))
            .toList(),
      ),
    );
  }

  /// 构建视频
  Widget _buildVideo(BuildContext context, VideoBlock block) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width,
          maxHeight: MediaQuery.of(context).size.height * 0.5, // 最大高度为屏幕高度的50%
        ),
        child: FijkVideoPlayerWidget(
          videoBlock: block,
        ),
      ),
    );
  }



  /// 构建音频
  Widget _buildAudio(BuildContext context, AudioBlock block) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Theme.of(context).colorScheme.surfaceContainer,
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.audiotrack,
              size: 32,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    block.title ?? '音频文件',
                    style: Theme.of(context).textTheme.titleSmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    block.src,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => _handleAudioTap(context, block),
              icon: const Icon(Icons.play_arrow),
              tooltip: '播放音频',
            ),
          ],
        ),
      ),
    );
  }

  /// 构建嵌入内容
  Widget _buildEmbed(BuildContext context, EmbedBlock block) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Container(
        height: block.height?.toDouble() ?? 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[100],
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _buildEmbedContent(context, block),
        ),
      ),
    );
  }

  /// 构建嵌入内容
  Widget _buildEmbedContent(BuildContext context, EmbedBlock block) {
    if (block.platform != null) {
      return _buildPlatformEmbed(context, block);
    }

    // 通用嵌入内容占位符
    return Container(
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.web,
            size: 48,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            block.title ?? '嵌入内容',
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            '点击查看',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              block.src,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
                fontSize: 10,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建平台嵌入内容
  Widget _buildPlatformEmbed(BuildContext context, EmbedBlock block) {
    String platformName = block.platform?.toUpperCase() ?? 'EMBED';

    return GestureDetector(
      onTap: () => _handleEmbedTap(context, block),
      child: Container(
        color: Colors.grey[100],
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.open_in_new,
              size: 48,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              '$platformName 内容',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            if (block.title != null) ...[
              const SizedBox(height: 8),
              Text(
                block.title!,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 8),
            Text(
              '点击打开',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理图片点击
  void _handleImageTap(BuildContext context, ImageBlock block) {
    if (allImageUrls != null && allImageUrls!.isNotEmpty) {
      // 如果有图片列表，使用自定义的垂直滑动画廊
      final currentIndex = imageIndex ?? allImageUrls!.indexOf(block.src);
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VerticalImageGalleryScreen(
            imageUrls: allImageUrls!,
            initialIndex: currentIndex >= 0 ? currentIndex : 0,
          ),
        ),
      );
    } else {
      // 如果没有图片列表，只显示当前图片
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VerticalImageGalleryScreen(
            imageUrls: [block.src],
            initialIndex: 0,
          ),
        ),
      );
    }
  }

  /// 处理音频点击
  void _handleAudioTap(BuildContext context, AudioBlock block) {
    _handleLinkTap(context, block.src);
  }

  /// 处理嵌入内容点击
  void _handleEmbedTap(BuildContext context, EmbedBlock block) {
    _handleLinkTap(context, block.src);
  }

  /// 处理链接点击
  void _handleLinkTap(BuildContext context, String url) {
    if (onLinkTap != null) {
      onLinkTap!();
      return;
    }

    // 处理相对链接和智能跳转
    _handleSmartLinkNavigation(context, url);
  }

  /// 智能链接导航处理
  void _handleSmartLinkNavigation(BuildContext context, String url) async {
    try {
      // 1. 处理相对链接，转换为绝对链接
      final absoluteUrl = _convertToAbsoluteUrl(url);

      // 2. 判断是否为内部链接（同域名）
      if (_isInternalLink(absoluteUrl)) {
        // 3. 根据URL特征判断跳转类型
        if (_isTagUrl(absoluteUrl)) {
          // 包含tag参数，跳转到分类列表页
          await _navigateToCategory(context, absoluteUrl);
        } else if (_isArticleUrl(absoluteUrl)) {
          // 文章URL，跳转到文章详情页
          await _navigateToArticle(context, absoluteUrl);
        } else {
          // 其他内部链接，使用外部浏览器打开
          await _openExternalUrl(context, absoluteUrl);
        }
      } else {
        // 外部链接，使用外部浏览器打开
        await _openExternalUrl(context, absoluteUrl);
      }
    } catch (e) {
      // 出错时显示错误信息
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('链接处理失败: $e')),
        );
      }
    }
  }

  /// 将相对链接转换为绝对链接
  String _convertToAbsoluteUrl(String url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url; // 已经是绝对链接
    }

    // 使用SettingsService获取baseUrl
    final settingsService = SettingsService.instance;
    return settingsService.getFullUrl(url);
  }

  /// 判断是否为内部链接
  bool _isInternalLink(String url) {
    try {
      final uri = Uri.parse(url);
      final settingsService = SettingsService.instance;
      final baseUri = Uri.parse(settingsService.baseUrl);

      return uri.host == baseUri.host;
    } catch (e) {
      return false;
    }
  }

  /// 判断是否为标签URL（包含tag参数）
  bool _isTagUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.queryParameters.containsKey('tag') ||
             uri.path.contains('/tag/') ||
             uri.path.contains('/category/');
    } catch (e) {
      return false;
    }
  }

  /// 判断是否为文章URL
  bool _isArticleUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.path.contains('/archives/') ||
             uri.path.contains('/post/') ||
             uri.path.contains('/article/');
    } catch (e) {
      return false;
    }
  }

  /// 导航到分类页面
  Future<void> _navigateToCategory(BuildContext context, String url) async {
    try {
      // 从URL中提取分类信息
      final categoryName = _extractCategoryFromUrl(url);

      // 创建Category对象
      final category = Category(
        id: categoryName.toLowerCase().replaceAll(' ', '_'),
        name: categoryName,
        url: url,
      );

      // 跳转到分类文章页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryArticlesScreen(category: category),
        ),
      );
    } catch (e) {
      // 如果解析失败，使用外部浏览器打开
      await _openExternalUrl(context, url);
    }
  }

  /// 导航到文章详情页面
  Future<void> _navigateToArticle(BuildContext context, String url) async {
    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 创建临时Article对象
      final article = Article(
        id: url.hashCode.toString(),
        title: '正在加载...',
        url: url,
        excerpt: '',
        author: '',
        publishDate: DateTime.now(),
        categories: [],
        content: '',
      );

      // 关闭加载指示器
      if (context.mounted) {
        Navigator.pop(context);
      }

      // 跳转到文章详情页面
      if (context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ArticleDetailScreen(article: article),
          ),
        );
      }
    } catch (e) {
      // 关闭加载指示器
      if (context.mounted) {
        Navigator.pop(context);
      }
      // 如果解析失败，使用外部浏览器打开
      await _openExternalUrl(context, url);
    }
  }

  /// 使用外部浏览器打开URL
  Future<void> _openExternalUrl(BuildContext context, String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('无法打开链接')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('打开链接失败: $e')),
        );
      }
    }
  }

  /// 从URL中提取分类名称
  String _extractCategoryFromUrl(String url) {
    try {
      final uri = Uri.parse(url);

      // 从查询参数中提取tag
      if (uri.queryParameters.containsKey('tag')) {
        return uri.queryParameters['tag'] ?? '未知分类';
      }

      // 从路径中提取分类
      final pathSegments = uri.pathSegments;
      if (pathSegments.contains('tag') && pathSegments.length > pathSegments.indexOf('tag') + 1) {
        return pathSegments[pathSegments.indexOf('tag') + 1];
      }

      if (pathSegments.contains('category') && pathSegments.length > pathSegments.indexOf('category') + 1) {
        return pathSegments[pathSegments.indexOf('category') + 1];
      }

      return '未知分类';
    } catch (e) {
      return '未知分类';
    }
  }
}
