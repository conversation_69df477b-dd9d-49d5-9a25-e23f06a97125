{"url": "https://agree.blinkit.top/archives/214691/", "page_size": 135123, "video_patterns": {"video_extensions": [], "streaming_urls": [], "video_ids": [], "player_configs": []}, "script_analysis": [{"src": null, "type": "text/javascript", "length": 146, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 1107, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": true, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 683, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 2190, "contains_video_keywords": ["play"], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": [], "content_sample": "\n(function () {\n    window.TypechoComment = {\n        dom : function (id) {\n            return document.getElementById(id);\n        },\n    \n        create : function (tag, attr) {\n            var el = document.createElement(tag);\n        \n            for (var key in attr) {\n                el.setAttribute(key, attr[key]);\n            }\n        \n            return el;\n        },\n\n        reply : function (cid, coid) {\n            var comment = this.dom(cid), parent = comment.parentNode,\n         ..."}, {"src": null, "type": "text/javascript", "length": 552, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "application/ld+json", "length": 698, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 1978, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 144, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 10485, "contains_video_keywords": [], "contains_ajax": true, "contains_fetch": false, "contains_base64": true, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 55, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 607, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 964, "contains_video_keywords": ["play"], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": [], "content_sample": "\n            var wrap = document.querySelector('#wrap');\n            var navbar = document.querySelector('#navbar');\n            wrap.classList.remove('display-menu-tree');\n            var body = document.querySelector('body');\n            body.classList.remove('display-menu-tree');\n            LocalConst.TOC_AT_LEFT = false;\n            LocalConst.ENABLE_MATH_JAX = false;\n            LocalConst.ENABLE_FLOW_CHART = false;\n            LocalConst.ENABLE_MERMAID = false;\n            \n              ..."}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 153, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 145, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 184, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 180, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 181, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 184, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 181, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 183, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 261, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 261, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 261, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 261, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 266, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 266, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 266, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 266, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 29, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 13, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 210, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 1860, "contains_video_keywords": [], "contains_ajax": true, "contains_fetch": false, "contains_base64": true, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 421, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 159, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": null, "length": 159, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}, {"src": null, "type": "text/javascript", "length": 5854, "contains_video_keywords": ["play"], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": [], "content_sample": "\n        $('#submitBtn').on('click', function(event) {\n            event.preventDefault();\n            var _page = parseInt($('#pageNum').val());\n            if(isNaN(_page)) {\n                layer.msg('输入页码不正确');\n                return false;\n            }\n            var url = window.location.href;\n\n            if(url.includes('author')) {\n                const urlPath = new URL(url).pathname;\n\n                const authorIndex = urlPath.indexOf('/author/');\n                if (authorIndex ==..."}, {"src": null, "type": null, "length": 3432, "contains_video_keywords": [], "contains_ajax": false, "contains_fetch": false, "contains_base64": false, "video_related_functions": [], "suspicious_strings": []}], "hidden_elements": [{"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250615/2025061515045996629.gif", "alt": "同城约炮", "id": "article-bottom-ads-0"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250123/2025012315563421945.gif", "alt": "催情迷药", "id": "article-bottom-ads-1"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250619/2025061921455024489.gif", "alt": "电子棋牌", "id": "article-bottom-ads-2"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250626/2025062616494592530.png", "alt": "梦幻宝可梦", "id": "article-bottom-ads-3"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250618/2025061811260057409.gif", "alt": "PG电子", "id": "article-bottom-ads-4"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250612/2025061216083576439.gif", "alt": "286娱乐", "id": "article-bottom-ads-5"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250707/2025070723224323856.gif", "alt": "3625彩票", "id": "article-bottom-ads-6"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250707/2025070723324874541.gif", "alt": "17直播", "id": "article-bottom-ads-7"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250211/2025021118102920883.gif", "alt": "77直播", "id": "article-bottom-ads-8"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/banner.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250613/2025061321050242978.gif", "alt": "金沙直播", "id": "article-bottom-ads-9"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250628/2025062817325146665.gif", "alt": "51选妃", "id": "article-bottom-img-app-0-0"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023213651968.gif", "alt": "小马拉大车", "id": "article-bottom-img-app-0-1"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023220176300.gif", "alt": "91破解版", "id": "article-bottom-img-app-0-2"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023223598150.gif", "alt": "茶馆约妹儿", "id": "article-bottom-img-app-0-3"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023230157679.gif", "alt": "TikTok成人版", "id": "article-bottom-img-app-0-4"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023232026508.gif", "alt": "51污漫", "id": "article-bottom-img-app-0-5"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023234829170.gif", "alt": "AI脱衣换脸", "id": "article-bottom-img-app-0-6"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023245780069.gif", "alt": "抖音Max", "id": "article-bottom-img-app-0-7"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023251947050.gif", "alt": "禁漫天堂", "id": "article-bottom-img-app-0-8"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250509/2025050916135970601.gif", "alt": "<PERSON><PERSON><PERSON><PERSON>", "id": "article-bottom-img-app-0-9"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023264696709.gif", "alt": "草榴app", "id": "article-bottom-img-app-1-0"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023211657016.gif", "alt": "免费推特", "id": "article-bottom-img-app-1-1"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023270724510.gif", "alt": "成人版快手", "id": "article-bottom-img-app-1-2"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023273298648.gif", "alt": "猎奇重口", "id": "article-bottom-img-app-1-3"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023280225759.gif", "alt": "换妻交友", "id": "article-bottom-img-app-1-4"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023304199158.gif", "alt": "男男视频", "id": "article-bottom-img-app-1-5"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023254627577.gif", "alt": "免费P站", "id": "article-bottom-img-app-1-6"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250704/2025070419464218506.jpeg", "alt": "萝莉岛破解", "id": "article-bottom-img-app-1-7"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023263142243.gif", "alt": "暗网禁区", "id": "article-bottom-img-app-1-8"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload/xiao/20240530/2024053016333774951.gif", "alt": "51成人导航", "id": "article-bottom-img-app-1-9"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload/xiao/20240530/2024053021370853952.jpeg", "alt": "免费微密圈", "id": "article-bottom-img-app-2-0"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20240925/2024092521533986941.jpeg", "alt": "YouTube成人版", "id": "article-bottom-img-app-2-1"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250627/2025062720024836162.png", "alt": "51约妹", "id": "article-bottom-img-app-2-2"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250118/2025011815231089757.png", "alt": "Soul", "id": "article-bottom-img-app-2-3"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload/xiao/20240530/2024053021385686824.jpeg", "alt": "免费VPN", "id": "article-bottom-img-app-2-4"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20241017/2024101722282755100.gif", "alt": "免费AV", "id": "article-bottom-img-app-2-5"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20241025/2024102517200242654.gif", "alt": "禁网视频", "id": "article-bottom-img-app-2-6"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20241204/2024120421162123547.png", "alt": "51吃瓜轻量版", "id": "article-bottom-img-app-2-7"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250620/2025062023232026508.gif", "alt": "成人污漫", "id": "article-bottom-img-app-2-8"}}, {"type": "data_attribute", "tag": "img", "attributes": {"src": "/usr/themes/Mirages/images/zw.png", "loading": "lazy", "data-src": "https://pic.duofyi.cn/upload_01/xiao/20250424/2025042419455998658.gif", "alt": "免费视频", "id": "article-bottom-img-app-2-9"}}], "data_attributes": [{"element": "div", "attribute": "data-config", "value": "{\"live\":false,\"autoplay\":false,\"theme\":\"#FADFA3\",\"loop\":false,\"screenshot\":false,\"hotkey\":true,\"preload\":\"metadata\",\"lang\":\"zh-cn\",\"logo\":null,\"volume\":0.69999999999999996,\"mutex\":true,\"video_ads_url\":\"\",\"ads_jump_url\":\"\",\"ads_jump_time\":-1,\"video\":{\"url\":\"https:\\/\\/hls.qzkj.tech\\/videos5\\/cdda34b968a6f855861510b486056103\\/cdda34b968a6f855861510b486056103.m3u8?auth_key=1751960084-686cca145503e-0-8bf4ee77f37b70fedbcda217caf53791&v=3&time=0\",\"type\":\"hls\",\"thumbnails\":null},\"open_danmaku\":\"1\"}"}], "base64_content": [{"content": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", "length": 65}], "external_resources": [{"type": "script", "src": "/usr/plugins/DPlayer/plugin/hls.min.js"}, {"type": "script", "src": "/usr/plugins/DPlayer/assets/DPlayer.min.js?v=2"}, {"type": "script", "src": "/usr/plugins/DPlayer/assets/player.js?v=1"}, {"type": "css", "href": "/usr/plugins/DPlayer/assets/DPlayer.min.css?v=1"}], "suspicious_patterns": {"encrypted_strings": ["\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\"", "'/usr/plugins/Mirages/biaoqing/paopao/'", "'/usr/plugins/Mirages/biaoqing/aru/'"], "obfuscated_code": [], "ajax_endpoints": [",\n        COMMENT_SYSTEM: 0,\n        OWO_API: ", ") !== -1) {\n            return true;\n        }\n        return false;\n    }\n\n    var loadBackgroundImage = function (bgUrl , bgEle){\n        if (is_cdnimg(bgUrl)) {\n            $.ajax(bgUrl, {\n                xhrFields: {responseType: ", "ajax-banner:done", "ajax-banner:done", ") > 0) {\n            $.ajax(imgUrl, {\n                xhrFields: {responseType: ", "video_ads_url", "video", "https:\\/\\/hls.qzkj.tech\\/videos5\\/cdda34b968a6f855861510b486056103\\/cdda34b968a6f855861510b486056103.m3u8?auth_key=1751960084-686cca145503e-0-8bf4ee77f37b70fedbcda217caf53791&v=3&time=0", "ajax", " data-api=", "/usr/themes/Mirages/", " + imageSrc + ", " + imageSrc + "], "api_calls": []}}