/// 内容块类型枚举
enum ContentBlockType {
  /// 文字段落
  paragraph,
  /// 标题（h1-h6）
  heading,
  /// 图片
  image,
  /// 视频
  video,
  /// 音频
  audio,
  /// 链接
  link,
  /// 引用块
  blockquote,
  /// 代码块
  codeBlock,
  /// 行内代码
  inlineCode,
  /// 列表项
  listItem,
  /// 有序列表
  orderedList,
  /// 无序列表
  unorderedList,
  /// 分割线
  divider,
  /// 换行
  lineBreak,
  /// 粗体文本
  bold,
  /// 斜体文本
  italic,
  /// 混合内容（包含多种内联元素的段落）
  mixed,
  /// 嵌入内容（iframe等）
  embed,
}

/// 标题级别枚举
enum HeadingLevel {
  h1(1),
  h2(2),
  h3(3),
  h4(4),
  h5(5),
  h6(6);

  const HeadingLevel(this.level);
  final int level;
}

/// 视频源
class VideoSource {
  final String src;
  final String? type;

  const VideoSource({
    required this.src,
    this.type,
  });

  factory VideoSource.fromMap(Map<String, dynamic> map) {
    return VideoSource(
      src: map['src'] ?? '',
      type: map['type'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'src': src,
      'type': type,
    };
  }
}

/// 音频源
class AudioSource {
  final String src;
  final String? type;

  const AudioSource({
    required this.src,
    this.type,
  });

  factory AudioSource.fromMap(Map<String, dynamic> map) {
    return AudioSource(
      src: map['src'] ?? '',
      type: map['type'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'src': src,
      'type': type,
    };
  }
}

/// 内容块基类
abstract class ContentBlock {
  final ContentBlockType type;
  final String? id;

  const ContentBlock({
    required this.type,
    this.id,
  });

  /// 从Map创建ContentBlock
  factory ContentBlock.fromMap(Map<String, dynamic> map) {
    final type = ContentBlockType.values.firstWhere(
      (e) => e.name == map['type'],
      orElse: () => ContentBlockType.paragraph,
    );

    switch (type) {
      case ContentBlockType.paragraph:
        return ParagraphBlock.fromMap(map);
      case ContentBlockType.heading:
        return HeadingBlock.fromMap(map);
      case ContentBlockType.image:
        return ImageBlock.fromMap(map);
      case ContentBlockType.video:
        return VideoBlock.fromMap(map);
      case ContentBlockType.audio:
        return AudioBlock.fromMap(map);
      case ContentBlockType.link:
        return LinkBlock.fromMap(map);
      case ContentBlockType.blockquote:
        return BlockquoteBlock.fromMap(map);
      case ContentBlockType.codeBlock:
        return CodeBlock.fromMap(map);
      case ContentBlockType.inlineCode:
        return InlineCodeBlock.fromMap(map);
      case ContentBlockType.listItem:
        return ListItemBlock.fromMap(map);
      case ContentBlockType.orderedList:
        return OrderedListBlock.fromMap(map);
      case ContentBlockType.unorderedList:
        return UnorderedListBlock.fromMap(map);
      case ContentBlockType.divider:
        return DividerBlock.fromMap(map);
      case ContentBlockType.lineBreak:
        return LineBreakBlock.fromMap(map);
      case ContentBlockType.bold:
        return BoldBlock.fromMap(map);
      case ContentBlockType.italic:
        return ItalicBlock.fromMap(map);
      case ContentBlockType.mixed:
        return MixedBlock.fromMap(map);
      case ContentBlockType.embed:
        return EmbedBlock.fromMap(map);
    }
  }

  /// 转换为Map
  Map<String, dynamic> toMap();
}

/// 文字段落块
class ParagraphBlock extends ContentBlock {
  final String text;
  final Map<String, String>? styles;

  const ParagraphBlock({
    required this.text,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.paragraph, id: id);

  factory ParagraphBlock.fromMap(Map<String, dynamic> map) {
    return ParagraphBlock(
      text: map['text'] ?? '',
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'text': text,
      'styles': styles,
      'id': id,
    };
  }
}

/// 标题块
class HeadingBlock extends ContentBlock {
  final String text;
  final HeadingLevel level;
  final Map<String, String>? styles;

  const HeadingBlock({
    required this.text,
    required this.level,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.heading, id: id);

  factory HeadingBlock.fromMap(Map<String, dynamic> map) {
    final levelValue = map['level'] ?? 1;
    final level = HeadingLevel.values.firstWhere(
      (e) => e.level == levelValue,
      orElse: () => HeadingLevel.h1,
    );

    return HeadingBlock(
      text: map['text'] ?? '',
      level: level,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'text': text,
      'level': level.level,
      'styles': styles,
      'id': id,
    };
  }
}

/// 图片块
class ImageBlock extends ContentBlock {
  final String src;
  final String? alt;
  final String? title;
  final double? width;
  final double? height;
  final Map<String, String>? styles;

  const ImageBlock({
    required this.src,
    this.alt,
    this.title,
    this.width,
    this.height,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.image, id: id);

  factory ImageBlock.fromMap(Map<String, dynamic> map) {
    return ImageBlock(
      src: map['src'] ?? '',
      alt: map['alt'],
      title: map['title'],
      width: map['width']?.toDouble(),
      height: map['height']?.toDouble(),
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'src': src,
      'alt': alt,
      'title': title,
      'width': width,
      'height': height,
      'styles': styles,
      'id': id,
    };
  }
}

/// 链接块
class LinkBlock extends ContentBlock {
  final String text;
  final String href;
  final String? title;
  final Map<String, String>? styles;

  const LinkBlock({
    required this.text,
    required this.href,
    this.title,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.link, id: id);

  factory LinkBlock.fromMap(Map<String, dynamic> map) {
    return LinkBlock(
      text: map['text'] ?? '',
      href: map['href'] ?? '',
      title: map['title'],
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'text': text,
      'href': href,
      'title': title,
      'styles': styles,
      'id': id,
    };
  }
}

/// 引用块
class BlockquoteBlock extends ContentBlock {
  final List<ContentBlock> children;
  final Map<String, String>? styles;

  const BlockquoteBlock({
    required this.children,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.blockquote, id: id);

  factory BlockquoteBlock.fromMap(Map<String, dynamic> map) {
    final childrenList = map['children'] as List<dynamic>? ?? [];
    final children = childrenList
        .map((child) => ContentBlock.fromMap(Map<String, dynamic>.from(child)))
        .toList();

    return BlockquoteBlock(
      children: children,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'children': children.map((child) => child.toMap()).toList(),
      'styles': styles,
      'id': id,
    };
  }
}

/// 代码块
class CodeBlock extends ContentBlock {
  final String code;
  final String? language;
  final Map<String, String>? styles;

  const CodeBlock({
    required this.code,
    this.language,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.codeBlock, id: id);

  factory CodeBlock.fromMap(Map<String, dynamic> map) {
    return CodeBlock(
      code: map['code'] ?? '',
      language: map['language'],
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'code': code,
      'language': language,
      'styles': styles,
      'id': id,
    };
  }
}

/// 行内代码块
class InlineCodeBlock extends ContentBlock {
  final String code;
  final Map<String, String>? styles;

  const InlineCodeBlock({
    required this.code,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.inlineCode, id: id);

  factory InlineCodeBlock.fromMap(Map<String, dynamic> map) {
    return InlineCodeBlock(
      code: map['code'] ?? '',
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'code': code,
      'styles': styles,
      'id': id,
    };
  }
}

/// 列表项块
class ListItemBlock extends ContentBlock {
  final List<ContentBlock> children;
  final Map<String, String>? styles;

  const ListItemBlock({
    required this.children,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.listItem, id: id);

  factory ListItemBlock.fromMap(Map<String, dynamic> map) {
    final childrenList = map['children'] as List<dynamic>? ?? [];
    final children = childrenList
        .map((child) => ContentBlock.fromMap(Map<String, dynamic>.from(child)))
        .toList();

    return ListItemBlock(
      children: children,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'children': children.map((child) => child.toMap()).toList(),
      'styles': styles,
      'id': id,
    };
  }
}

/// 有序列表块
class OrderedListBlock extends ContentBlock {
  final List<ListItemBlock> items;
  final int? start;
  final Map<String, String>? styles;

  const OrderedListBlock({
    required this.items,
    this.start,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.orderedList, id: id);

  factory OrderedListBlock.fromMap(Map<String, dynamic> map) {
    final itemsList = map['items'] as List<dynamic>? ?? [];
    final items = itemsList
        .map((item) => ListItemBlock.fromMap(Map<String, dynamic>.from(item)))
        .toList();

    return OrderedListBlock(
      items: items,
      start: map['start'],
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'items': items.map((item) => item.toMap()).toList(),
      'start': start,
      'styles': styles,
      'id': id,
    };
  }
}

/// 无序列表块
class UnorderedListBlock extends ContentBlock {
  final List<ListItemBlock> items;
  final Map<String, String>? styles;

  const UnorderedListBlock({
    required this.items,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.unorderedList, id: id);

  factory UnorderedListBlock.fromMap(Map<String, dynamic> map) {
    final itemsList = map['items'] as List<dynamic>? ?? [];
    final items = itemsList
        .map((item) => ListItemBlock.fromMap(Map<String, dynamic>.from(item)))
        .toList();

    return UnorderedListBlock(
      items: items,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'items': items.map((item) => item.toMap()).toList(),
      'styles': styles,
      'id': id,
    };
  }
}

/// 分割线块
class DividerBlock extends ContentBlock {
  final Map<String, String>? styles;

  const DividerBlock({
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.divider, id: id);

  factory DividerBlock.fromMap(Map<String, dynamic> map) {
    return DividerBlock(
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'styles': styles,
      'id': id,
    };
  }
}

/// 换行块
class LineBreakBlock extends ContentBlock {
  const LineBreakBlock({String? id}) : super(type: ContentBlockType.lineBreak, id: id);

  factory LineBreakBlock.fromMap(Map<String, dynamic> map) {
    return LineBreakBlock(id: map['id']);
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'id': id,
    };
  }
}

/// 粗体文本块
class BoldBlock extends ContentBlock {
  final String text;
  final Map<String, String>? styles;

  const BoldBlock({
    required this.text,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.bold, id: id);

  factory BoldBlock.fromMap(Map<String, dynamic> map) {
    return BoldBlock(
      text: map['text'] ?? '',
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'text': text,
      'styles': styles,
      'id': id,
    };
  }
}

/// 斜体文本块
class ItalicBlock extends ContentBlock {
  final String text;
  final Map<String, String>? styles;

  const ItalicBlock({
    required this.text,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.italic, id: id);

  factory ItalicBlock.fromMap(Map<String, dynamic> map) {
    return ItalicBlock(
      text: map['text'] ?? '',
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'text': text,
      'styles': styles,
      'id': id,
    };
  }
}

/// 混合内容块（包含多种内联元素）
class MixedBlock extends ContentBlock {
  final List<ContentBlock> children;
  final Map<String, String>? styles;

  const MixedBlock({
    required this.children,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.mixed, id: id);

  factory MixedBlock.fromMap(Map<String, dynamic> map) {
    final childrenList = map['children'] as List<dynamic>? ?? [];
    final children = childrenList
        .map((child) => ContentBlock.fromMap(Map<String, dynamic>.from(child)))
        .toList();

    return MixedBlock(
      children: children,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'children': children.map((child) => child.toMap()).toList(),
      'styles': styles,
      'id': id,
    };
  }
}

/// 视频块
class VideoBlock extends ContentBlock {
  final String src;
  final String? poster;
  final String? alt;
  final String? title;
  final double? width;
  final double? height;
  final bool controls;
  final bool autoplay;
  final bool loop;
  final bool muted;
  final String? platform; // youtube, bilibili, vimeo等
  final List<VideoSource>? sources;
  final Map<String, String>? styles;

  const VideoBlock({
    required this.src,
    this.poster,
    this.alt,
    this.title,
    this.width,
    this.height,
    this.controls = true,
    this.autoplay = false,
    this.loop = false,
    this.muted = false,
    this.platform,
    this.sources,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.video, id: id);

  factory VideoBlock.fromMap(Map<String, dynamic> map) {
    final sourcesList = map['sources'] as List<dynamic>? ?? [];
    final sources = sourcesList.isNotEmpty
        ? sourcesList
            .map((source) => VideoSource.fromMap(Map<String, dynamic>.from(source)))
            .toList()
        : null;

    return VideoBlock(
      src: map['src'] ?? '',
      poster: map['poster'],
      alt: map['alt'],
      title: map['title'],
      width: map['width']?.toDouble(),
      height: map['height']?.toDouble(),
      controls: map['controls'] ?? true,
      autoplay: map['autoplay'] ?? false,
      loop: map['loop'] ?? false,
      muted: map['muted'] ?? false,
      platform: map['platform'],
      sources: sources,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'src': src,
      'poster': poster,
      'alt': alt,
      'title': title,
      'width': width,
      'height': height,
      'controls': controls,
      'autoplay': autoplay,
      'loop': loop,
      'muted': muted,
      'platform': platform,
      'sources': sources?.map((source) => source.toMap()).toList(),
      'styles': styles,
      'id': id,
    };
  }
}

/// 音频块
class AudioBlock extends ContentBlock {
  final String src;
  final String? title;
  final bool controls;
  final bool autoplay;
  final bool loop;
  final bool muted;
  final List<AudioSource>? sources;
  final Map<String, String>? styles;

  const AudioBlock({
    required this.src,
    this.title,
    this.controls = true,
    this.autoplay = false,
    this.loop = false,
    this.muted = false,
    this.sources,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.audio, id: id);

  factory AudioBlock.fromMap(Map<String, dynamic> map) {
    final sourcesList = map['sources'] as List<dynamic>? ?? [];
    final sources = sourcesList.isNotEmpty
        ? sourcesList
            .map((source) => AudioSource.fromMap(Map<String, dynamic>.from(source)))
            .toList()
        : null;

    return AudioBlock(
      src: map['src'] ?? '',
      title: map['title'],
      controls: map['controls'] ?? true,
      autoplay: map['autoplay'] ?? false,
      loop: map['loop'] ?? false,
      muted: map['muted'] ?? false,
      sources: sources,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'src': src,
      'title': title,
      'controls': controls,
      'autoplay': autoplay,
      'loop': loop,
      'muted': muted,
      'sources': sources?.map((source) => source.toMap()).toList(),
      'styles': styles,
      'id': id,
    };
  }
}

/// 嵌入内容块（iframe等）
class EmbedBlock extends ContentBlock {
  final String src;
  final String? title;
  final double? width;
  final double? height;
  final String? platform; // youtube, bilibili等
  final String? embedType; // iframe, embed, object
  final Map<String, String>? attributes;
  final Map<String, String>? styles;

  const EmbedBlock({
    required this.src,
    this.title,
    this.width,
    this.height,
    this.platform,
    this.embedType,
    this.attributes,
    this.styles,
    String? id,
  }) : super(type: ContentBlockType.embed, id: id);

  factory EmbedBlock.fromMap(Map<String, dynamic> map) {
    return EmbedBlock(
      src: map['src'] ?? '',
      title: map['title'],
      width: map['width']?.toDouble(),
      height: map['height']?.toDouble(),
      platform: map['platform'],
      embedType: map['embedType'],
      attributes: map['attributes'] != null ? Map<String, String>.from(map['attributes']) : null,
      styles: map['styles'] != null ? Map<String, String>.from(map['styles']) : null,
      id: map['id'],
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'src': src,
      'title': title,
      'width': width,
      'height': height,
      'platform': platform,
      'embedType': embedType,
      'attributes': attributes,
      'styles': styles,
      'id': id,
    };
  }
}
