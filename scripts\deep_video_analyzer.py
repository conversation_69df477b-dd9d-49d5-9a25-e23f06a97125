#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度视频分析脚本
专门用于分析网页中可能隐藏的视频内容
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import base64

class DeepVideoAnalyzer:
    def __init__(self):
        self.base_url = "https://agree.blinkit.top"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def analyze_url(self, url):
        """深度分析单个URL"""
        print(f"🔍 深度分析URL: {url}")
        
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            
            print(f"📄 页面大小: {len(html_content)} 字符")
            
            # 分析结果
            analysis = {
                'url': url,
                'page_size': len(html_content),
                'video_patterns': self._find_video_patterns(html_content),
                'script_analysis': self._analyze_scripts(soup),
                'hidden_elements': self._find_hidden_elements(soup),
                'data_attributes': self._find_data_attributes(soup),
                'base64_content': self._find_base64_content(html_content),
                'external_resources': self._find_external_resources(soup),
                'suspicious_patterns': self._find_suspicious_patterns(html_content)
            }
            
            self._print_analysis(analysis)
            return analysis
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def _find_video_patterns(self, html_content):
        """查找视频相关的模式"""
        patterns = {
            'video_extensions': [],
            'streaming_urls': [],
            'video_ids': [],
            'player_configs': []
        }
        
        # 视频文件扩展名
        video_ext_pattern = r'https?://[^\s"\'<>]+\.(?:mp4|webm|ogg|avi|mov|wmv|flv|m4v|mkv|3gp)'
        patterns['video_extensions'] = re.findall(video_ext_pattern, html_content, re.IGNORECASE)
        
        # 流媒体URL
        streaming_patterns = [
            r'https?://[^\s"\'<>]*(?:youtube\.com|youtu\.be)[^\s"\'<>]*',
            r'https?://[^\s"\'<>]*bilibili\.com[^\s"\'<>]*',
            r'https?://[^\s"\'<>]*vimeo\.com[^\s"\'<>]*',
            r'https?://[^\s"\'<>]*dailymotion\.com[^\s"\'<>]*'
        ]
        
        for pattern in streaming_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            patterns['streaming_urls'].extend(matches)
        
        # 视频ID模式
        video_id_patterns = [
            r'video[_-]?id["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'videoId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'vid["\']?\s*[:=]\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in video_id_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            patterns['video_ids'].extend(matches)
        
        # 播放器配置
        player_patterns = [
            r'player\s*[=:]\s*\{[^}]+\}',
            r'videoConfig\s*[=:]\s*\{[^}]+\}',
            r'playerOptions\s*[=:]\s*\{[^}]+\}'
        ]
        
        for pattern in player_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            patterns['player_configs'].extend(matches)
        
        return patterns
    
    def _analyze_scripts(self, soup):
        """深度分析JavaScript代码"""
        scripts_info = []
        
        for script in soup.find_all('script'):
            script_content = script.get_text()
            if not script_content.strip():
                continue
            
            script_info = {
                'src': script.get('src'),
                'type': script.get('type'),
                'length': len(script_content),
                'contains_video_keywords': self._check_video_keywords(script_content),
                'contains_ajax': 'ajax' in script_content.lower() or 'xhr' in script_content.lower(),
                'contains_fetch': 'fetch(' in script_content.lower(),
                'contains_base64': 'base64' in script_content.lower() or 'btoa' in script_content.lower() or 'atob' in script_content.lower(),
                'video_related_functions': self._find_video_functions(script_content),
                'suspicious_strings': self._find_suspicious_strings(script_content)
            }
            
            # 如果脚本包含视频相关内容，保存部分内容用于分析
            if script_info['contains_video_keywords'] or script_info['video_related_functions']:
                script_info['content_sample'] = script_content[:500] + '...' if len(script_content) > 500 else script_content
            
            scripts_info.append(script_info)
        
        return scripts_info
    
    def _check_video_keywords(self, content):
        """检查视频相关关键词"""
        keywords = [
            'video', 'mp4', 'webm', 'player', 'play', 'pause', 'duration',
            'currentTime', 'videoElement', 'mediaSource', 'blob:', 'stream',
            'loadVideo', 'playVideo', 'videoUrl', 'videoSrc', 'videoData'
        ]
        
        content_lower = content.lower()
        found_keywords = [kw for kw in keywords if kw in content_lower]
        return found_keywords
    
    def _find_video_functions(self, content):
        """查找视频相关函数"""
        function_patterns = [
            r'function\s+\w*[Vv]ideo\w*\s*\([^)]*\)',
            r'function\s+\w*[Pp]lay\w*\s*\([^)]*\)',
            r'function\s+\w*[Ll]oad\w*\s*\([^)]*\)',
            r'\w*[Vv]ideo\w*\s*[:=]\s*function\s*\([^)]*\)'
        ]
        
        functions = []
        for pattern in function_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            functions.extend(matches)
        
        return functions
    
    def _find_suspicious_strings(self, content):
        """查找可疑字符串"""
        patterns = [
            r'["\'][^"\']*(?:mp4|webm|m3u8|flv)[^"\']*["\']',
            r'["\']https?://[^"\']*video[^"\']*["\']',
            r'["\']https?://[^"\']*stream[^"\']*["\']',
            r'["\']blob:[^"\']*["\']'
        ]
        
        suspicious = []
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            suspicious.extend(matches)
        
        return suspicious
    
    def _find_hidden_elements(self, soup):
        """查找隐藏元素"""
        hidden_elements = []
        
        # 查找隐藏的video标签
        hidden_videos = soup.find_all('video', style=re.compile(r'display\s*:\s*none', re.IGNORECASE))
        for video in hidden_videos:
            hidden_elements.append({
                'type': 'hidden_video',
                'src': video.get('src'),
                'attributes': dict(video.attrs)
            })
        
        # 查找data-*属性中可能包含视频信息的元素
        elements_with_data = soup.find_all(attrs={'data-video': True})
        elements_with_data.extend(soup.find_all(attrs={'data-src': True}))
        elements_with_data.extend(soup.find_all(attrs={'data-url': True}))
        
        for elem in elements_with_data:
            hidden_elements.append({
                'type': 'data_attribute',
                'tag': elem.name,
                'attributes': dict(elem.attrs)
            })
        
        return hidden_elements
    
    def _find_data_attributes(self, soup):
        """查找data属性"""
        data_attrs = []
        
        for elem in soup.find_all():
            for attr_name, attr_value in elem.attrs.items():
                if attr_name.startswith('data-') and attr_value:
                    if any(keyword in str(attr_value).lower() for keyword in ['video', 'mp4', 'stream', 'play']):
                        data_attrs.append({
                            'element': elem.name,
                            'attribute': attr_name,
                            'value': attr_value
                        })
        
        return data_attrs
    
    def _find_base64_content(self, html_content):
        """查找Base64编码内容"""
        base64_patterns = [
            r'data:video/[^;]+;base64,([A-Za-z0-9+/=]+)',
            r'["\']([A-Za-z0-9+/=]{50,})["\']'  # 长Base64字符串
        ]
        
        base64_content = []
        for pattern in base64_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if len(match) > 50:  # 只保存较长的Base64字符串
                    base64_content.append({
                        'content': match[:100] + '...' if len(match) > 100 else match,
                        'length': len(match)
                    })
        
        return base64_content
    
    def _find_external_resources(self, soup):
        """查找外部资源"""
        resources = []
        
        # JavaScript文件
        for script in soup.find_all('script', src=True):
            src = script.get('src')
            if src and any(keyword in src.lower() for keyword in ['video', 'player', 'media']):
                resources.append({
                    'type': 'script',
                    'src': src
                })
        
        # CSS文件
        for link in soup.find_all('link', href=True):
            href = link.get('href')
            if href and any(keyword in href.lower() for keyword in ['video', 'player', 'media']):
                resources.append({
                    'type': 'css',
                    'href': href
                })
        
        return resources
    
    def _find_suspicious_patterns(self, html_content):
        """查找可疑模式"""
        patterns = {
            'encrypted_strings': [],
            'obfuscated_code': [],
            'ajax_endpoints': [],
            'api_calls': []
        }
        
        # 加密字符串模式
        encrypted_patterns = [
            r'["\'][A-Za-z0-9+/=]{30,}["\']',  # Base64-like
            r'["\'][a-f0-9]{32,}["\']',        # MD5/SHA-like
        ]
        
        for pattern in encrypted_patterns:
            matches = re.findall(pattern, html_content)
            patterns['encrypted_strings'].extend(matches[:5])  # 只保存前5个
        
        # 混淆代码模式
        if re.search(r'eval\s*\(', html_content, re.IGNORECASE):
            patterns['obfuscated_code'].append('Contains eval()')
        
        if re.search(r'\\x[0-9a-f]{2}', html_content, re.IGNORECASE):
            patterns['obfuscated_code'].append('Contains hex encoding')
        
        # AJAX端点
        ajax_patterns = [
            r'["\']([^"\']*(?:api|ajax|video|stream)[^"\']*)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in ajax_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            patterns['ajax_endpoints'].extend(matches[:10])  # 只保存前10个
        
        return patterns
    
    def _print_analysis(self, analysis):
        """打印分析结果"""
        print(f"\n📊 深度分析结果:")
        print(f"  📄 页面大小: {analysis['page_size']} 字符")
        
        # 视频模式
        video_patterns = analysis['video_patterns']
        print(f"\n🎥 视频模式分析:")
        print(f"  视频文件: {len(video_patterns['video_extensions'])} 个")
        for video in video_patterns['video_extensions'][:3]:
            print(f"    - {video}")
        
        print(f"  流媒体URL: {len(video_patterns['streaming_urls'])} 个")
        for url in video_patterns['streaming_urls'][:3]:
            print(f"    - {url}")
        
        print(f"  视频ID: {len(video_patterns['video_ids'])} 个")
        for vid in video_patterns['video_ids'][:3]:
            print(f"    - {vid}")
        
        # 脚本分析
        video_scripts = [s for s in analysis['script_analysis'] if s['contains_video_keywords']]
        print(f"\n📜 脚本分析:")
        print(f"  包含视频关键词的脚本: {len(video_scripts)} 个")
        
        for script in video_scripts[:2]:
            print(f"    脚本长度: {script['length']}")
            print(f"    关键词: {script['contains_video_keywords']}")
            if script.get('content_sample'):
                print(f"    内容示例: {script['content_sample'][:100]}...")
        
        # 隐藏元素
        print(f"\n🔍 隐藏元素: {len(analysis['hidden_elements'])} 个")
        for elem in analysis['hidden_elements'][:3]:
            print(f"    - {elem['type']}: {elem}")
        
        # 可疑模式
        suspicious = analysis['suspicious_patterns']
        print(f"\n⚠️  可疑模式:")
        print(f"  加密字符串: {len(suspicious['encrypted_strings'])} 个")
        print(f"  混淆代码: {len(suspicious['obfuscated_code'])} 个")
        print(f"  AJAX端点: {len(suspicious['ajax_endpoints'])} 个")

def main():
    analyzer = DeepVideoAnalyzer()
    
    # 测试URL
    test_urls = [
        "https://agree.blinkit.top/archives/214691/",
        "https://agree.blinkit.top/archives/214491/",
        "https://agree.blinkit.top/archives/214603/"
    ]
    
    for url in test_urls:
        result = analyzer.analyze_url(url)
        if result:
            # 保存详细结果
            filename = f"deep_analysis_{url.split('/')[-2]}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"💾 详细结果已保存到: {filename}")
        
        print("\n" + "="*80 + "\n")

if __name__ == '__main__':
    main()
