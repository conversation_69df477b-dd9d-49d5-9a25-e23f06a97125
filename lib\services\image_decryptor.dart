import 'dart:typed_data';
import 'package:encrypt/encrypt.dart';

class ImageDecryptor {
  // 从Android代码直接转换的密钥和IV字节数组
  static final _key = Key(Uint8List.fromList([
    102, 53, 100, 57, 54, 53, 100, 102,
    55, 53, 51, 51, 54, 50, 55, 48
  ]));

  static final _iv = IV(Uint8List.fromList([
    57, 55, 98, 54, 48, 51, 57, 52,
    97, 98, 99, 50, 102, 98, 101, 49
  ]));

  // 使用CBC模式和PKCS7填充，与Android代码一致
  static final _encrypter = Encrypter(AES(_key, mode: AESMode.cbc, padding: 'PKCS7'));

  /// 解密图片字节数组
  static Uint8List? decryptImageBytes(Uint8List encryptedBytes) {
    try {
      print('Attempting to decrypt image, size: ${encryptedBytes.length} bytes');
      print('Key bytes: ${_key.bytes}');
      print('IV bytes: ${_iv.bytes}');

      // 检查数据长度
      if (encryptedBytes.length < 16) {
        print('Image data too short for decryption: ${encryptedBytes.length} bytes');
        return null;
      }

      // 检查数据长度是否是16的倍数（AES块大小）
      if (encryptedBytes.length % 16 != 0) {
        print('Warning: Data length ${encryptedBytes.length} is not a multiple of 16');
        // 尝试填充到16的倍数
        final paddingLength = 16 - (encryptedBytes.length % 16);
        final paddedBytes = Uint8List(encryptedBytes.length + paddingLength);
        paddedBytes.setRange(0, encryptedBytes.length, encryptedBytes);
        encryptedBytes = paddedBytes;
        print('Padded data to ${encryptedBytes.length} bytes');
      }

      // 尝试多种解密方法

      // 方法1: 标准AES/CBC/PKCS7
      try {
        final encrypted = Encrypted(encryptedBytes);
        final decryptedBytes = _encrypter.decryptBytes(encrypted, iv: _iv);
        print('Method 1 (AES/CBC/PKCS7) successful, decrypted size: ${decryptedBytes.length} bytes');
        return Uint8List.fromList(decryptedBytes);
      } catch (e) {
        print('Method 1 failed: $e');
      }

      // 方法2: 尝试不同的填充模式
      try {
        final encrypter2 = Encrypter(AES(_key, mode: AESMode.cbc, padding: null));
        final encrypted = Encrypted(encryptedBytes);
        final decryptedBytes = encrypter2.decryptBytes(encrypted, iv: _iv);
        print('Method 2 (AES/CBC/NoPadding) successful, decrypted size: ${decryptedBytes.length} bytes');
        return Uint8List.fromList(decryptedBytes);
      } catch (e) {
        print('Method 2 failed: $e');
      }

      // 方法3: 尝试ECB模式
      try {
        final encrypter3 = Encrypter(AES(_key, mode: AESMode.ecb));
        final encrypted = Encrypted(encryptedBytes);
        final decryptedBytes = encrypter3.decryptBytes(encrypted);
        print('Method 3 (AES/ECB) successful, decrypted size: ${decryptedBytes.length} bytes');
        return Uint8List.fromList(decryptedBytes);
      } catch (e) {
        print('Method 3 failed: $e');
      }

      print('All decryption methods failed');
      return null;
    } catch (e) {
      print('Image decryption failed: $e');
      print('Original data size: ${encryptedBytes.length} bytes');
      print('First 16 bytes: ${encryptedBytes.take(16).toList()}');
      print('Last 16 bytes: ${encryptedBytes.skip(encryptedBytes.length - 16).toList()}');
      return null;
    }
  }

  /// 检查字节数组是否为有效的图片格式
  static bool isValidImageBytes(Uint8List bytes) {
    if (bytes.length < 4) return false;
    
    // 检查常见图片格式的文件头
    // JPEG: FF D8 FF
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return true;
    }
    
    // PNG: 89 50 4E 47
    if (bytes.length >= 8 && 
        bytes[0] == 0x89 && bytes[1] == 0x50 && 
        bytes[2] == 0x4E && bytes[3] == 0x47) {
      return true;
    }
    
    // GIF: 47 49 46 38
    if (bytes.length >= 6 &&
        bytes[0] == 0x47 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x38) {
      return true;
    }
    
    // WebP: 52 49 46 46 ... 57 45 42 50
    if (bytes.length >= 12 &&
        bytes[0] == 0x52 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && 
        bytes[10] == 0x42 && bytes[11] == 0x50) {
      return true;
    }
    
    return false;
  }
}
