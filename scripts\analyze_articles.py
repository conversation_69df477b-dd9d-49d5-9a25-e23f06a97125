#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的文章分析脚本
从首页获取文章列表，然后分析每个详情页的视频内容
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from html_analyzer import HTMLAnalyzer
import time
import sys

class ArticleAnalyzer:
    def __init__(self):
        self.base_url = "https://agree.blinkit.top"
        self.category_url = f"{self.base_url}/category/wpcz/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.html_analyzer = HTMLAnalyzer()
    
    def fetch_articles_from_homepage(self, page=1):
        """从首页获取文章列表"""
        try:
            url = self.category_url if page == 1 else f"{self.category_url}{page}/"
            print(f"正在获取首页文章列表: {url}")
            
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            return self._parse_article_list(response.text)
        except requests.RequestException as e:
            print(f"获取首页失败: {e}")
            return []
    
    def _parse_article_list(self, html):
        """解析文章列表"""
        soup = BeautifulSoup(html, 'html.parser')
        articles = []
        
        # 查找文章元素
        article_elements = soup.select('article[itemtype="http://schema.org/BlogPosting"]')
        print(f"找到 {len(article_elements)} 篇文章")
        
        for article in article_elements:
            try:
                # 提取标题
                title_element = article.select_one('h2.post-card-title')
                if not title_element:
                    continue
                
                title = title_element.get_text().strip()
                if not title or title == "热搜 HOT":
                    continue
                
                # 提取链接
                link_element = article.select_one('a[href^="/archives/"]')
                if not link_element:
                    continue
                
                href = link_element.get('href')
                if not href:
                    continue
                
                # 构建完整URL
                full_url = f"{self.base_url}{href}"
                
                # 提取文章ID
                id_match = re.search(r'/archives/(\d+)/', href)
                article_id = id_match.group(1) if id_match else ''
                
                if not article_id:
                    continue
                
                # 提取图片URL
                image_url = self._extract_image_url(article)
                
                article_info = {
                    'id': article_id,
                    'title': title,
                    'url': full_url,
                    'image_url': image_url
                }
                
                articles.append(article_info)
                print(f"文章 {len(articles)}: {title}")
                print(f"  URL: {full_url}")
                
            except Exception as e:
                print(f"解析文章失败: {e}")
                continue
        
        return articles
    
    def _extract_image_url(self, article_element):
        """提取文章图片URL"""
        try:
            script_elements = article_element.select('script')
            for script in script_elements:
                script_text = script.get_text()
                if 'loadBannerDirect' in script_text:
                    # 使用正则表达式提取图片URL
                    image_url_regex = re.compile(r"'(https://pic.*?)'")
                    match = image_url_regex.search(script_text)
                    if match:
                        return match.group(1)
        except Exception as e:
            print(f"提取图片URL失败: {e}")
        return None
    
    def analyze_article_videos(self, article):
        """分析单篇文章的视频内容"""
        print(f"\n{'='*80}")
        print(f"正在分析文章: {article['title']}")
        print(f"URL: {article['url']}")
        print('='*80)
        
        try:
            # 获取文章详情页HTML
            html_content = self.html_analyzer.fetch_html(article['url'])
            if not html_content:
                print("无法获取文章内容")
                return None
            
            # 分析媒体内容
            soup = BeautifulSoup(html_content, 'html.parser')
            analysis_result = self.html_analyzer.analyze_media_content(soup)
            
            # 打印分析结果
            self._print_analysis_result(analysis_result)
            
            # 保存详细结果
            output_file = f"article_{article['id']}_analysis.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'article': article,
                    'analysis': analysis_result
                }, f, ensure_ascii=False, indent=2)
            
            print(f"详细分析结果已保存到: {output_file}")
            
            return analysis_result
            
        except Exception as e:
            print(f"分析文章失败: {e}")
            return None
    
    def _print_analysis_result(self, result):
        """打印分析结果摘要"""
        print(f"\n📊 分析结果摘要:")
        print(f"  🎥 视频数量: {len(result['videos'])}")
        print(f"  🖼️  图片数量: {len(result['images'])}")
        print(f"  🎵 音频数量: {len(result['audio'])}")
        print(f"  📦 嵌入内容数量: {len(result['embeds'])}")
        
        # 详细显示视频信息
        if result['videos']:
            print(f"\n🎥 视频详情:")
            for i, video in enumerate(result['videos'], 1):
                print(f"  视频 {i}:")
                print(f"    类型: {video['type']}")
                print(f"    链接: {video.get('src', 'N/A')}")
                if video.get('platform'):
                    print(f"    平台: {video['platform']}")
                if video.get('poster'):
                    print(f"    封面: {video['poster']}")
                print()
        
        # 显示图片信息（只显示前3个）
        if result['images']:
            print(f"🖼️  图片详情 (前3个):")
            for i, image in enumerate(result['images'][:3], 1):
                print(f"  图片 {i}: {image.get('src', 'N/A')}")
                if image.get('alt'):
                    print(f"    描述: {image['alt']}")
        
        # 显示音频信息
        if result['audio']:
            print(f"\n🎵 音频详情:")
            for i, audio in enumerate(result['audio'], 1):
                print(f"  音频 {i}: {audio.get('src', 'N/A')}")
        
        # 显示嵌入内容信息
        if result['embeds']:
            print(f"\n📦 嵌入内容详情:")
            for i, embed in enumerate(result['embeds'], 1):
                print(f"  嵌入 {i}: {embed.get('src', embed.get('data', 'N/A'))}")
                if embed.get('mime_type'):
                    print(f"    类型: {embed['mime_type']}")
    
    def analyze_multiple_articles(self, max_articles=5, page=1):
        """分析多篇文章"""
        print(f"开始分析文章，最多分析 {max_articles} 篇")
        
        # 获取文章列表
        articles = self.fetch_articles_from_homepage(page)
        if not articles:
            print("没有找到文章")
            return
        
        # 限制分析数量
        articles_to_analyze = articles[:max_articles]
        
        print(f"\n将分析以下 {len(articles_to_analyze)} 篇文章:")
        for i, article in enumerate(articles_to_analyze, 1):
            print(f"{i}. {article['title']}")
        
        # 分析每篇文章
        results = []
        for i, article in enumerate(articles_to_analyze, 1):
            print(f"\n进度: {i}/{len(articles_to_analyze)}")
            result = self.analyze_article_videos(article)
            if result:
                results.append({
                    'article': article,
                    'analysis': result
                })
            
            # 添加延时避免请求过快
            if i < len(articles_to_analyze):
                print("等待2秒...")
                time.sleep(2)
        
        # 保存汇总结果
        summary_file = f"articles_analysis_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 分析完成！汇总结果已保存到: {summary_file}")
        
        # 打印汇总统计
        self._print_summary_stats(results)
    
    def _print_summary_stats(self, results):
        """打印汇总统计"""
        total_videos = sum(len(r['analysis']['videos']) for r in results)
        total_images = sum(len(r['analysis']['images']) for r in results)
        total_audio = sum(len(r['analysis']['audio']) for r in results)
        total_embeds = sum(len(r['analysis']['embeds']) for r in results)
        
        print(f"\n📈 汇总统计:")
        print(f"  分析文章数: {len(results)}")
        print(f"  总视频数: {total_videos}")
        print(f"  总图片数: {total_images}")
        print(f"  总音频数: {total_audio}")
        print(f"  总嵌入内容数: {total_embeds}")
        
        # 统计视频平台
        video_platforms = {}
        for result in results:
            for video in result['analysis']['videos']:
                platform = video.get('platform', 'unknown')
                video_platforms[platform] = video_platforms.get(platform, 0) + 1
        
        if video_platforms:
            print(f"\n🎥 视频平台分布:")
            for platform, count in video_platforms.items():
                print(f"  {platform}: {count}")

def main():
    analyzer = ArticleAnalyzer()
    
    if len(sys.argv) > 1:
        max_articles = int(sys.argv[1])
    else:
        max_articles = 3  # 默认分析3篇文章
    
    if len(sys.argv) > 2:
        page = int(sys.argv[2])
    else:
        page = 1  # 默认第1页
    
    print(f"文章视频分析工具")
    print(f"将分析最多 {max_articles} 篇文章（第 {page} 页）")
    
    analyzer.analyze_multiple_articles(max_articles, page)

if __name__ == '__main__':
    main()
