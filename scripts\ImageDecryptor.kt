import android.util.Log
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object ImageDecryptor {

    // 从JavaScript代码转换的密钥和IV
    private val KEY = byteArrayOf(
        102, 53, 100, 57, 54, 53, 100, 102,
        55, 53, 51, 51, 54, 50, 55, 48
    ).map { it }.toByteArray()

    private val IV = byteArrayOf(
        57, 55, 98, 54, 48, 51, 57, 52,
        97, 98, 99, 50, 102, 98, 101, 49
    ).map { it }.toByteArray()

    /**
     * 直接解密字节数组
     */
    fun decryptRawBytes(encryptedBytes: ByteArray): ByteArray? {
        return try {
            // 1. 创建密钥
            val keySpec = SecretKeySpec(KEY, "AES")
            
            // 2. 创建IV
            val ivSpec = IvParameterSpec(IV)
            
            // 3. 初始化解密器
            val cipher = Cipher.getInstance("AES/CBC/PKCS7Padding")
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec)
            
            // 4. 直接解密字节数组
            cipher.doFinal(encryptedBytes)
        } catch (e: Exception) {
            Log.e("ImageDecryptor", "Raw decryption failed", e)
            null
        }
    }
}