#!/usr/bin/env python3
"""
验证Flutter应用图标是否正确生成
"""

import os
from pathlib import Path

def verify_android_icons():
    """验证Android图标文件"""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Android图标路径
    android_res_path = project_root / "android" / "app" / "src" / "main" / "res"
    
    # 需要检查的密度文件夹
    density_folders = [
        "mipmap-mdpi",
        "mipmap-hdpi", 
        "mipmap-xhdpi",
        "mipmap-xxhdpi",
        "mipmap-xxxhdpi"
    ]
    
    print("🔍 验证Android图标文件...")
    
    all_icons_exist = True
    
    for folder in density_folders:
        folder_path = android_res_path / folder
        icon_path = folder_path / "launcher_icon.png"
        
        if icon_path.exists():
            file_size = icon_path.stat().st_size
            print(f"✅ {folder}/launcher_icon.png - {file_size} bytes")
        else:
            print(f"❌ {folder}/launcher_icon.png - 文件不存在")
            all_icons_exist = False
    
    return all_icons_exist

def verify_manifest():
    """验证AndroidManifest.xml配置"""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    manifest_path = project_root / "android" / "app" / "src" / "main" / "AndroidManifest.xml"
    
    print("\n🔍 验证AndroidManifest.xml配置...")
    
    if not manifest_path.exists():
        print("❌ AndroidManifest.xml 文件不存在")
        return False
    
    with open(manifest_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if '@mipmap/launcher_icon' in content:
        print("✅ 图标引用正确: @mipmap/launcher_icon")
    else:
        print("❌ 图标引用不正确")
        return False
    
    if '吃瓜App' in content:
        print("✅ 应用名称正确: 吃瓜App")
    else:
        print("❌ 应用名称未更新")
        return False
    
    return True

def verify_assets():
    """验证资源文件"""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print("\n🔍 验证资源文件...")
    
    # 检查SVG文件
    svg_path = project_root / "assets" / "logo" / "app_logo.svg"
    if svg_path.exists():
        print(f"✅ SVG logo存在: {svg_path}")
    else:
        print(f"❌ SVG logo不存在: {svg_path}")
    
    # 检查PNG文件
    png_path = project_root / "assets" / "logo" / "app_logo_1024.png"
    if png_path.exists():
        file_size = png_path.stat().st_size
        print(f"✅ PNG logo存在: {png_path} - {file_size} bytes")
    else:
        print(f"❌ PNG logo不存在: {png_path}")

if __name__ == "__main__":
    print("🚀 开始验证Flutter应用图标...")
    
    # 验证资源文件
    verify_assets()
    
    # 验证Android图标
    android_ok = verify_android_icons()
    
    # 验证配置文件
    manifest_ok = verify_manifest()
    
    print("\n" + "="*50)
    if android_ok and manifest_ok:
        print("🎉 所有图标文件和配置都正确!")
        print("📱 现在可以运行 'flutter run' 来测试新图标")
    else:
        print("⚠️  存在一些问题，请检查上面的错误信息")
    print("="*50)
